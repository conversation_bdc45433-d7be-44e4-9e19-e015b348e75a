{"openapi": "3.0.1", "info": {"title": "JucieAnd<PERSON>lower", "version": "v1"}, "paths": {"/api/Auth/register": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserRegisterDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserRegisterDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserRegisterDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Auth/logout": {"post": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK"}}}}, "/api/Auth/refresh-token": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TokenModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TokenModel"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Auth/update-profile": {"put": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserUpdateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserUpdateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserUpdateDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Auth/profile": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK"}}}}, "/api/Auth/verify-email": {"get": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "email", "in": "query", "schema": {"type": "string"}}, {"name": "token", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Cart": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CartItemCreateDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CartItemCreateDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CartItemCreateDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Cart/{productId}": {"delete": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "productId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Category": {"get": {"tags": ["Category"], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Category"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CategoryNoID"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CategoryNoID"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CategoryNoID"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Category/{id}": {"get": {"tags": ["Category"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["Category"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CategoryNoID"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CategoryNoID"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CategoryNoID"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Category"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Orders": {"get": {"tags": ["Orders"], "parameters": [{"name": "id", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Orders/payment-return": {"get": {"tags": ["Orders"], "responses": {"200": {"description": "OK"}}}}, "/api/Orders/from-cart": {"post": {"tags": ["Orders"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderFromCartDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrderFromCartDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/OrderFromCartDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Payment": {"get": {"tags": ["Payment"], "responses": {"200": {"description": "OK"}}}}, "/api/ProductDetail": {"get": {"tags": ["ProductDetail"], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["ProductDetail"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductDetailCreateDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductDetailCreateDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProductDetailCreateDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/ProductDetail/{id}": {"get": {"tags": ["ProductDetail"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["ProductDetail"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductDetailCreateDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductDetailCreateDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProductDetailCreateDTO"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["ProductDetail"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Products": {"get": {"tags": ["Products"], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Products"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductCreateUpdateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductCreateUpdateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProductCreateUpdateDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Products/{id}": {"get": {"tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductCreateUpdateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductCreateUpdateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProductCreateUpdateDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Promotion": {"get": {"tags": ["Promotion"], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Promotion"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PromotionCreateUpdateDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PromotionCreateUpdateDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PromotionCreateUpdateDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Promotion/{id}": {"get": {"tags": ["Promotion"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["Promotion"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PromotionCreateUpdateDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PromotionCreateUpdateDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PromotionCreateUpdateDTO"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Promotion"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Revenue/daily": {"get": {"tags": ["Revenue"], "parameters": [{"name": "startDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "endDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Revenue/monthly": {"get": {"tags": ["Revenue"], "parameters": [{"name": "year", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Revenue/stats": {"get": {"tags": ["Revenue"], "responses": {"200": {"description": "OK"}}}}, "/api/Revenue/top-products": {"get": {"tags": ["Revenue"], "parameters": [{"name": "limit", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "startDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "endDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Revenue/order-status-stats": {"get": {"tags": ["Revenue"], "parameters": [{"name": "startDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "endDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Workshop": {"get": {"tags": ["Workshop"], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Workshop"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkshopDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkshopDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WorkshopDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Workshop/{id}": {"get": {"tags": ["Workshop"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["Workshop"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkshopDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkshopDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WorkshopDTO"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Workshop"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/WorkshopTicket": {"get": {"tags": ["WorkshopTicket"], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["WorkshopTicket"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkshopTicketDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkshopTicketDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WorkshopTicketDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/WorkshopTicket/{id}": {"get": {"tags": ["WorkshopTicket"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["WorkshopTicket"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkshopTicketDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkshopTicketDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WorkshopTicketDTO"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["WorkshopTicket"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}}, "components": {"schemas": {"CartItemCreateDTO": {"type": "object", "properties": {"ProductId": {"type": "integer", "format": "int32"}, "Quantity": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "CategoryNoID": {"type": "object", "properties": {"Name": {"type": "string", "nullable": true}, "Description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "LoginRequest": {"type": "object", "properties": {"Email": {"type": "string", "nullable": true}, "Password": {"type": "string", "nullable": true}}, "additionalProperties": false}, "OrderFromCartDTO": {"type": "object", "properties": {"DeliveryAddress": {"type": "string", "nullable": true}, "PromotionCode": {"type": "string", "nullable": true}, "Note": {"type": "string", "nullable": true}, "SelectedCartItemIds": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "ProductCreateUpdateDto": {"type": "object", "properties": {"Name": {"type": "string", "nullable": true}, "Description": {"type": "string", "nullable": true}, "Price": {"type": "number", "format": "double"}, "ImageUrl": {"type": "string", "nullable": true}, "CategoryId": {"type": "integer", "format": "int32"}, "IsAvailable": {"type": "boolean"}}, "additionalProperties": false}, "ProductDetailCreateDTO": {"type": "object", "properties": {"ProductId": {"type": "integer", "format": "int32", "nullable": true}, "Size": {"type": "string", "nullable": true}, "Color": {"type": "string", "nullable": true}, "FlowerType": {"type": "string", "nullable": true}, "ExtraPrice": {"type": "number", "format": "double", "nullable": true}}, "additionalProperties": false}, "PromotionCreateUpdateDTO": {"type": "object", "properties": {"Code": {"type": "string", "nullable": true}, "Description": {"type": "string", "nullable": true}, "DiscountPercent": {"type": "integer", "format": "int32", "nullable": true}, "MaxDiscount": {"type": "number", "format": "double", "nullable": true}, "StartDate": {"type": "string", "format": "date", "nullable": true}, "EndDate": {"type": "string", "format": "date", "nullable": true}, "IsActive": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "TokenModel": {"type": "object", "properties": {"AccessToken": {"type": "string", "nullable": true}, "RefreshToken": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserRegisterDto": {"type": "object", "properties": {"FullName": {"type": "string", "nullable": true}, "Email": {"type": "string", "nullable": true}, "Password": {"type": "string", "nullable": true}, "Phone": {"type": "string", "nullable": true}, "Address": {"type": "string", "nullable": true}, "Gender": {"type": "string", "nullable": true}, "BirthDate": {"type": "string", "format": "date", "nullable": true}}, "additionalProperties": false}, "UserUpdateDto": {"type": "object", "properties": {"FullName": {"type": "string", "nullable": true}, "Phone": {"type": "string", "nullable": true}, "Address": {"type": "string", "nullable": true}, "Gender": {"type": "string", "nullable": true}, "BirthDate": {"type": "string", "format": "date", "nullable": true}, "OldPassword": {"type": "string", "nullable": true}, "NewPassword": {"type": "string", "nullable": true}, "ConfirmPassword": {"type": "string", "nullable": true}}, "additionalProperties": false}, "WorkshopDTO": {"type": "object", "properties": {"Title": {"type": "string", "nullable": true}, "Description": {"type": "string", "nullable": true}, "Location": {"type": "string", "nullable": true}, "EventDate": {"type": "string", "format": "date-time", "nullable": true}, "MaxAttendees": {"type": "integer", "format": "int32", "nullable": true}, "Price": {"type": "number", "format": "double", "nullable": true}, "ImageUrl": {"type": "string", "nullable": true}}, "additionalProperties": false}, "WorkshopTicketDTO": {"type": "object", "properties": {"WorkshopId": {"type": "integer", "format": "int32", "nullable": true}, "TicketType": {"type": "string", "nullable": true}, "Price": {"type": "number", "format": "double", "nullable": true}, "Quantity": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "JWT Authorization header using the Bear<PERSON> scheme (Example: 'Bearer <token>')", "name": "Authorization", "in": "header"}}}, "security": [{"Bearer": []}]}