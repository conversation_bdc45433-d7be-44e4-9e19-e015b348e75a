import React, { useState, useEffect } from "react";
import {
  Container,
  Paper,
  Typography,
  Box,
  Grid,
  Card,
  CardContent,
  Chip,
  Button,
  CircularProgress,
  Alert,
  Divider,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Avatar,
} from "@mui/material";
import { 
  Receipt, 
  LocalShipping, 
  Payment, 
  ArrowBack,
  ShoppingCart,
  CalendarToday,
  LocationOn,
  Note
} from "@mui/icons-material";
import { useAppSelector } from "../../stores/hooks";
import { ordersAPI } from "../../services/api";
import { toast } from "react-toastify";
import { useNavigate, useParams } from "react-router-dom";

interface OrderDetail {
  orderDetailId: number;
  orderId: number;
  productId: number;
  quantity: number;
  unitPrice: number;
  product?: {
    productId: number;
    name: string;
    imageUrl: string;
    price: number;
  };
}

interface Order {
  orderId: number;
  orderDate: string;
  totalAmount: number;
  discountAmount: number;
  finalAmount: number;
  status: string;
  note: string;
  deliveryAddress: string;
  promotionCode?: string;
  orderDetails?: OrderDetail[];
}

const OrderDetailPage = () => {
  const { isAuthenticated } = useAppSelector((state) => state.Authentication);
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();

  const [order, setOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/auth/login');
      return;
    }
    if (id) {
      loadOrderDetail(parseInt(id));
    }
  }, [isAuthenticated, navigate, id]);

  const loadOrderDetail = async (orderId: number) => {
    try {
      setLoading(true);
      setError("");
      const orderData = await ordersAPI.getOrderById(orderId);
      setOrder(orderData);
    } catch (error: any) {
      console.error('Error loading order detail:', error);
      const errorMessage = error.message || "Không thể tải chi tiết đơn hàng";
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'pending':
        return 'warning';
      case 'completed':
      case 'paid':
        return 'success';
      case 'cancelled':
      case 'cancel':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'pending':
        return 'Đang xử lý';
      case 'completed':
        return 'Hoàn thành';
      case 'paid':
        return 'Đã thanh toán';
      case 'cancelled':
      case 'cancel':
        return 'Đã hủy';
      default:
        return status;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (!isAuthenticated) {
    return (
      <Container maxWidth="md" sx={{ mt: 4 }}>
        <Alert severity="warning">
          Vui lòng đăng nhập để xem đơn hàng
        </Alert>
      </Container>
    );
  }

  if (loading) {
    return (
      <Container maxWidth="md" sx={{ mt: 4, textAlign: "center" }}>
        <CircularProgress />
        <Typography variant="h6" sx={{ mt: 2 }}>
          Đang tải chi tiết đơn hàng...
        </Typography>
      </Container>
    );
  }

  if (error || !order) {
    return (
      <Container maxWidth="md" sx={{ mt: 4 }}>
        <Alert severity="error" sx={{ mb: 3 }}>
          {error || "Không tìm thấy đơn hàng"}
        </Alert>
        <Button
          variant="contained"
          startIcon={<ArrowBack />}
          onClick={() => navigate("/orders")}
          sx={{
            bgcolor: "#9e655c",
            "&:hover": { bgcolor: "#8a5a52" },
          }}
        >
          Quay lại danh sách đơn hàng
        </Button>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {/* Header */}
      <Paper
        elevation={0}
        sx={{
          mb: 4,
          p: 4,
          background: "linear-gradient(135deg, #9e655c 0%, #b8756b 100%)",
          color: "white",
          borderRadius: 4,
          position: "relative",
          overflow: "hidden",
        }}
      >
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box display="flex" alignItems="center">
            <Box
              sx={{
                bgcolor: "rgba(255,255,255,0.2)",
                borderRadius: "50%",
                p: 1.5,
                mr: 3,
              }}
            >
              <Receipt sx={{ fontSize: 32, color: "white" }} />
            </Box>
            <Box>
              <Typography variant="h4" fontWeight="bold" sx={{ mb: 0.5 }}>
                Đơn hàng #{order.orderId}
              </Typography>
              <Typography variant="body1" sx={{ opacity: 0.9 }}>
                Chi tiết đơn hàng của bạn
              </Typography>
            </Box>
          </Box>
          <Chip
            label={getStatusText(order.status)}
            color={getStatusColor(order.status) as any}
            sx={{ 
              fontWeight: 'bold',
              fontSize: '1rem',
              px: 2,
              py: 1,
              bgcolor: 'rgba(255,255,255,0.2)',
              color: 'white',
              border: '1px solid rgba(255,255,255,0.3)'
            }}
          />
        </Box>
      </Paper>

      <Button
        variant="outlined"
        startIcon={<ArrowBack />}
        onClick={() => navigate("/orders")}
        sx={{
          mb: 3,
          borderColor: "#9e655c",
          color: "#9e655c",
          "&:hover": {
            borderColor: "#8a5a52",
            bgcolor: "rgba(158, 101, 92, 0.04)",
          },
        }}
      >
        Quay lại danh sách đơn hàng
      </Button>

      <Grid container spacing={3}>
        {/* Order Information */}
        <Grid item xs={12} md={6}>
          <Card elevation={2} sx={{ borderRadius: 4, height: '100%' }}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" fontWeight="bold" color="#9e655c" mb={2}>
                Thông tin đơn hàng
              </Typography>
              
              <Box display="flex" alignItems="center" mb={2}>
                <CalendarToday sx={{ mr: 2, color: "#9e655c" }} />
                <Box>
                  <Typography variant="body2" fontWeight="bold">
                    Ngày đặt hàng
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {formatDate(order.orderDate)}
                  </Typography>
                </Box>
              </Box>

              <Box display="flex" alignItems="center" mb={2}>
                <LocationOn sx={{ mr: 2, color: "#9e655c" }} />
                <Box>
                  <Typography variant="body2" fontWeight="bold">
                    Địa chỉ giao hàng
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {order.deliveryAddress}
                  </Typography>
                </Box>
              </Box>

              {order.note && (
                <Box display="flex" alignItems="flex-start" mb={2}>
                  <Note sx={{ mr: 2, color: "#9e655c", mt: 0.5 }} />
                  <Box>
                    <Typography variant="body2" fontWeight="bold">
                      Ghi chú
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {order.note}
                    </Typography>
                  </Box>
                </Box>
              )}

              {order.promotionCode && (
                <Box>
                  <Typography variant="body2" fontWeight="bold" mb={0.5}>
                    Mã giảm giá
                  </Typography>
                  <Chip 
                    label={order.promotionCode} 
                    color="success" 
                    variant="outlined"
                    size="small"
                  />
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Payment Summary */}
        <Grid item xs={12} md={6}>
          <Card elevation={2} sx={{ borderRadius: 4, height: '100%' }}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" fontWeight="bold" color="#9e655c" mb={2}>
                Thông tin thanh toán
              </Typography>
              
              <Box display="flex" justifyContent="space-between" mb={1}>
                <Typography variant="body2">Tổng tiền hàng:</Typography>
                <Typography variant="body2" fontWeight="bold">
                  {formatCurrency(order.totalAmount)}
                </Typography>
              </Box>

              {order.discountAmount > 0 && (
                <Box display="flex" justifyContent="space-between" mb={1}>
                  <Typography variant="body2" color="success.main">Giảm giá:</Typography>
                  <Typography variant="body2" color="success.main" fontWeight="bold">
                    -{formatCurrency(order.discountAmount)}
                  </Typography>
                </Box>
              )}

              <Divider sx={{ my: 2 }} />

              <Box display="flex" justifyContent="space-between" mb={2}>
                <Typography variant="h6" fontWeight="bold">Tổng thanh toán:</Typography>
                <Typography variant="h6" color="#9e655c" fontWeight="bold">
                  {formatCurrency(order.finalAmount)}
                </Typography>
              </Box>

              <Box display="flex" alignItems="center">
                <Payment sx={{ mr: 1, color: "#9e655c" }} />
                <Typography variant="body2" color="text.secondary">
                  Thanh toán qua VNPay
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Order Items */}
        {order.orderDetails && order.orderDetails.length > 0 && (
          <Grid item xs={12}>
            <Card elevation={2} sx={{ borderRadius: 4 }}>
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h6" fontWeight="bold" color="#9e655c" mb={2}>
                  Sản phẩm đã đặt
                </Typography>
                
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Sản phẩm</TableCell>
                        <TableCell align="center">Số lượng</TableCell>
                        <TableCell align="right">Đơn giá</TableCell>
                        <TableCell align="right">Thành tiền</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {order.orderDetails.map((item) => (
                        <TableRow key={item.orderDetailId}>
                          <TableCell>
                            <Box display="flex" alignItems="center">
                              <Avatar
                                src={item.product?.imageUrl}
                                sx={{ mr: 2, width: 50, height: 50 }}
                              >
                                <ShoppingCart />
                              </Avatar>
                              <Typography variant="body2" fontWeight="bold">
                                {item.product?.name || `Sản phẩm #${item.productId}`}
                              </Typography>
                            </Box>
                          </TableCell>
                          <TableCell align="center">
                            <Typography variant="body2">
                              {item.quantity}
                            </Typography>
                          </TableCell>
                          <TableCell align="right">
                            <Typography variant="body2">
                              {formatCurrency(item.unitPrice)}
                            </Typography>
                          </TableCell>
                          <TableCell align="right">
                            <Typography variant="body2" fontWeight="bold">
                              {formatCurrency(item.quantity * item.unitPrice)}
                            </Typography>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>
          </Grid>
        )}
      </Grid>
    </Container>
  );
};

export default OrderDetailPage;
