import React, { useState } from "react";
import {
  Container,
  Paper,
  Typography,
  Box,
  Grid,
  TextField,
  Button,
  Divider,
  Alert,
  CircularProgress,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
} from "@mui/material";
import { Payment, LocalShipping, Receipt } from "@mui/icons-material";
import { useAppSelector, useAppDispatch } from "../../stores/hooks";
import { clearCart } from "../../stores/reducers/Cart";
import { ordersAPI } from "../../services/api";
import { toast } from "react-toastify";
import { useNavigate } from "react-router-dom";

const CheckoutPage = () => {
  const { items, totalAmount } = useAppSelector((state) => state.Cart);
  const { user, isAuthenticated } = useAppSelector((state) => state.Authentication);
  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  const [orderData, setOrderData] = useState({
    note: "",
    deliveryAddress: user?.address || "",
    promotionCode: "",
  });

  const handleInputChange = (field: string, value: string) => {
    setOrderData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");

    if (!orderData.deliveryAddress.trim()) {
      setError("Vui lòng nhập địa chỉ giao hàng");
      return;
    }

    if (items.length === 0) {
      setError("Giỏ hàng trống");
      return;
    }

    try {
      setLoading(true);
      console.log('Creating order with data:', orderData);
      const order = await ordersAPI.createFromCart(orderData);
      console.log('Order created successfully:', order);

      // Clear cart after successful order
      dispatch(clearCart());

      toast.success("Đặt hàng thành công! Chuyển hướng đến trang thanh toán...");

      // Handle payment URL from VNPay
      if (order && order.paymentUrl) {
        console.log('Redirecting to payment URL:', order.paymentUrl);
        // Redirect to VNPay payment page
        window.location.href = order.paymentUrl;
      } else if (order && order.orderId) {
        navigate(`/orders/${order.orderId}`);
      } else if (order && order.OrderId) {
        navigate(`/orders/${order.OrderId}`);
      } else {
        // Fallback to orders list if no orderId
        navigate('/orders');
      }
    } catch (error: any) {
      console.error('Error creating order:', error);
      const errorMessage = error.message || "Có lỗi xảy ra khi đặt hàng. Vui lòng thử lại.";
      setError(errorMessage);
      toast.error(`Đặt hàng thất bại: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  if (!isAuthenticated) {
    return (
      <Container maxWidth="md" sx={{ mt: 4 }}>
        <Alert severity="warning">
          Vui lòng đăng nhập để thanh toán
        </Alert>
      </Container>
    );
  }

  if (items.length === 0) {
    return (
      <Container maxWidth="md" sx={{ mt: 4 }}>
        <Alert severity="info">
          Giỏ hàng trống. <Button onClick={() => navigate("/flower")}>Tiếp tục mua sắm</Button>
        </Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {/* Header */}
      <Paper
        elevation={0}
        sx={{
          p: 4,
          mb: 4,
          background: "linear-gradient(135deg, #9e655c 0%, #b8766b 100%)",
          color: "white",
          borderRadius: 4,
          position: "relative",
          overflow: "hidden",
          "&::before": {
            content: '""',
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: "url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><circle cx=\"20\" cy=\"20\" r=\"2\" fill=\"rgba(255,255,255,0.1)\"/><circle cx=\"80\" cy=\"80\" r=\"2\" fill=\"rgba(255,255,255,0.1)\"/><circle cx=\"40\" cy=\"60\" r=\"1\" fill=\"rgba(255,255,255,0.1)\"/></svg>')",
            opacity: 0.3,
          },
        }}
      >
        <Box display="flex" alignItems="center" sx={{ position: "relative", zIndex: 1 }}>
          <Box
            sx={{
              bgcolor: "rgba(255,255,255,0.2)",
              borderRadius: "50%",
              p: 1.5,
              mr: 3,
            }}
          >
            <Payment sx={{ fontSize: 32, color: "white" }} />
          </Box>
          <Box>
            <Typography variant="h4" fontWeight="bold" sx={{ mb: 0.5 }}>
              Thanh toán
            </Typography>
            <Typography variant="body1" sx={{ opacity: 0.9, fontSize: "1.1rem" }}>
              Hoàn tất đơn hàng của bạn
            </Typography>
          </Box>
        </Box>
      </Paper>

      {error && (
        <Alert
          severity="error"
          sx={{
            mb: 3,
            borderRadius: 3,
            "& .MuiAlert-icon": {
              color: "#d32f2f",
            },
          }}
        >
          {error}
        </Alert>
      )}

      <Grid container spacing={4} sx={{ alignItems: "flex-start" }}>
        <Grid item xs={12} md={8}>
          <Box sx={{ display: "flex", flexDirection: "column", gap: 3 }}>
            <Paper
              elevation={0}
              sx={{
                p: 4,
                borderRadius: 4,
                background: "linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)",
                border: "1px solid rgba(158, 101, 92, 0.1)",
                height: "fit-content",
              }}
            >
            <Box display="flex" alignItems="center" mb={3}>
              <Box
                sx={{
                  bgcolor: "rgba(158, 101, 92, 0.1)",
                  borderRadius: "50%",
                  p: 1,
                  mr: 2,
                }}
              >
                <LocalShipping sx={{ fontSize: 24, color: "#9e655c" }} />
              </Box>
              <Typography variant="h6" sx={{ color: "#9e655c", fontWeight: 600 }}>
                Thông tin giao hàng
              </Typography>
            </Box>

            <Box component="form" onSubmit={handleSubmit} sx={{ display: "flex", flexDirection: "column", gap: 3 }}>
              <TextField
                fullWidth
                label="Họ và tên"
                value={user?.fullName || ""}
                disabled
                variant="outlined"
                sx={{
                  "& .MuiOutlinedInput-root": {
                    backgroundColor: "rgba(158, 101, 92, 0.05)",
                    borderRadius: 3,
                  },
                  "& .MuiInputLabel-root": {
                    color: "#9e655c",
                    fontWeight: 600,
                  },
                }}
              />

              <TextField
                fullWidth
                label="Số điện thoại"
                value={user?.phone || ""}
                disabled
                variant="outlined"
                sx={{
                  "& .MuiOutlinedInput-root": {
                    backgroundColor: "rgba(158, 101, 92, 0.05)",
                    borderRadius: 3,
                  },
                  "& .MuiInputLabel-root": {
                    color: "#9e655c",
                    fontWeight: 600,
                  },
                }}
              />

              <TextField
                fullWidth
                label="Địa chỉ giao hàng"
                value={orderData.deliveryAddress}
                onChange={(e) => handleInputChange("deliveryAddress", e.target.value)}
                required
                multiline
                rows={3}
                placeholder="Nhập địa chỉ giao hàng chi tiết"
                sx={{
                  "& .MuiOutlinedInput-root": {
                    backgroundColor: "rgba(255, 255, 255, 0.8)",
                    borderRadius: 3,
                    "&:hover fieldset": {
                      borderColor: "#9e655c",
                    },
                    "&.Mui-focused fieldset": {
                      borderColor: "#9e655c",
                    },
                  },
                  "& .MuiInputLabel-root": {
                    color: "#9e655c",
                    fontWeight: 600,
                  },
                }}
              />

              <TextField
                fullWidth
                label="Ghi chú đơn hàng"
                value={orderData.note}
                onChange={(e) => handleInputChange("note", e.target.value)}
                multiline
                rows={3}
                placeholder="Ghi chú thêm cho đơn hàng (tùy chọn)"
                sx={{
                  "& .MuiOutlinedInput-root": {
                    backgroundColor: "rgba(255, 255, 255, 0.8)",
                    borderRadius: 3,
                    "&:hover fieldset": {
                      borderColor: "#9e655c",
                    },
                    "&.Mui-focused fieldset": {
                      borderColor: "#9e655c",
                    },
                  },
                  "& .MuiInputLabel-root": {
                    color: "#9e655c",
                    fontWeight: 600,
                  },
                }}
              />

              <TextField
                fullWidth
                label="Mã khuyến mãi"
                value={orderData.promotionCode}
                onChange={(e) => handleInputChange("promotionCode", e.target.value)}
                placeholder="Nhập mã khuyến mãi (nếu có)"
                sx={{
                  "& .MuiOutlinedInput-root": {
                    backgroundColor: "rgba(255, 255, 255, 0.8)",
                    borderRadius: 3,
                    "&:hover fieldset": {
                      borderColor: "#9e655c",
                    },
                    "&.Mui-focused fieldset": {
                      borderColor: "#9e655c",
                    },
                  },
                  "& .MuiInputLabel-root": {
                    color: "#9e655c",
                    fontWeight: 600,
                  },
                }}
              />
            </Box>
          </Paper>

          <Paper
            elevation={0}
            sx={{
              p: 4,
              borderRadius: 4,
              background: "linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)",
              border: "1px solid rgba(158, 101, 92, 0.1)",
              height: "fit-content",
            }}
          >
            <Box display="flex" alignItems="center" mb={3}>
              <Box
                sx={{
                  bgcolor: "rgba(158, 101, 92, 0.1)",
                  borderRadius: "50%",
                  p: 1,
                  mr: 2,
                }}
              >
                <Receipt sx={{ fontSize: 24, color: "#9e655c" }} />
              </Box>
              <Typography variant="h6" sx={{ color: "#9e655c", fontWeight: 600 }}>
                Phương thức thanh toán
              </Typography>
            </Box>

            <Card
              variant="outlined"
              sx={{
                borderRadius: 3,
                border: "2px solid #9e655c",
                background: "linear-gradient(135deg, rgba(158, 101, 92, 0.05) 0%, rgba(184, 118, 107, 0.05) 100%)",
                transition: "all 0.3s ease",
                "&:hover": {
                  transform: "translateY(-2px)",
                  boxShadow: "0 8px 16px rgba(158, 101, 92, 0.15)",
                },
              }}
            >
              <CardContent sx={{ p: 3 }}>
                <Box display="flex" alignItems="center" mb={2}>
                  <Box
                    sx={{
                      bgcolor: "#9e655c",
                      borderRadius: 2,
                      p: 1,
                      mr: 2,
                    }}
                  >
                    <Payment sx={{ fontSize: 24, color: "white" }} />
                  </Box>
                  <Typography variant="h6" sx={{ color: "#9e655c", fontWeight: 700 }}>
                    Thanh toán qua VNPay
                  </Typography>
                </Box>
                <Typography variant="body1" color="text.secondary" sx={{ lineHeight: 1.6 }}>
                  Thanh toán an toàn và bảo mật qua cổng VNPay
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                  Hỗ trợ: ATM, Visa, MasterCard, QR Code
                </Typography>
              </CardContent>
            </Card>
          </Paper>
          </Box>
        </Grid>

        <Grid item xs={12} md={4}>
          <Paper
            elevation={0}
            sx={{
              p: 4,
              position: "sticky",
              top: 20,
              borderRadius: 4,
              background: "linear-gradient(135deg, #9e655c 0%, #b8766b 100%)",
              color: "white",
              border: "1px solid rgba(158, 101, 92, 0.2)",
              height: "fit-content",
              minHeight: "600px",
              display: "flex",
              flexDirection: "column",
            }}
          >
            <Typography variant="h5" gutterBottom sx={{ fontWeight: 700, mb: 3 }}>
              Đơn hàng của bạn
            </Typography>

            <Box
              sx={{
                bgcolor: "rgba(255,255,255,0.1)",
                borderRadius: 3,
                p: 2,
                mb: 3,
                backdropFilter: "blur(10px)",
                maxHeight: 250,
                overflowY: "auto",
                flex: "0 0 auto",
              }}
            >
              {items.map((item) => (
                <Box
                  key={item.id}
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    mb: 2,
                    p: 2,
                    bgcolor: "rgba(255,255,255,0.1)",
                    borderRadius: 2,
                    "&:last-child": { mb: 0 },
                  }}
                >
                  <Avatar
                    src={item.product?.imageUrl}
                    alt={item.product?.name}
                    variant="rounded"
                    sx={{
                      width: 50,
                      height: 50,
                      mr: 2,
                      border: "2px solid rgba(255,255,255,0.3)",
                    }}
                  />
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="body1" sx={{ fontWeight: 600, mb: 0.5 }}>
                      {item.product?.name}
                    </Typography>
                    <Typography variant="body2" sx={{ opacity: 0.8 }}>
                      Số lượng: {item.quantity}
                    </Typography>
                  </Box>
                  <Typography variant="body1" sx={{ fontWeight: 700 }}>
                    {((item.product?.price || 0) * item.quantity).toLocaleString("vi-VN")}đ
                  </Typography>
                </Box>
              ))}
            </Box>

            <Box
              sx={{
                bgcolor: "rgba(255,255,255,0.1)",
                borderRadius: 3,
                p: 3,
                mb: 3,
                backdropFilter: "blur(10px)",
                flex: "0 0 auto",
              }}
            >
              <Box display="flex" justifyContent="space-between" mb={2}>
                <Typography sx={{ opacity: 0.9 }}>Tạm tính:</Typography>
                <Typography sx={{ fontWeight: 600 }}>{totalAmount.toLocaleString("vi-VN")}đ</Typography>
              </Box>

              <Box display="flex" justifyContent="space-between" mb={2}>
                <Typography sx={{ opacity: 0.9 }}>Phí vận chuyển:</Typography>
                <Typography sx={{ fontWeight: 600, color: "#4caf50" }}>Miễn phí</Typography>
              </Box>

              <Box display="flex" justifyContent="space-between" mb={2}>
                <Typography sx={{ opacity: 0.9 }}>Giảm giá:</Typography>
                <Typography sx={{ fontWeight: 600 }}>0đ</Typography>
              </Box>

              <Divider sx={{ my: 2, bgcolor: "rgba(255,255,255,0.3)" }} />

              <Box display="flex" justifyContent="space-between" mb={1}>
                <Typography variant="h6" sx={{ fontWeight: 700 }}>Tổng cộng:</Typography>
                <Typography variant="h6" sx={{ fontWeight: 700, fontSize: "1.4rem" }}>
                  {totalAmount.toLocaleString("vi-VN")}đ
                </Typography>
              </Box>
            </Box>

            <Box sx={{ flex: "1 1 auto", display: "flex", flexDirection: "column", justifyContent: "flex-end", gap: 2 }}>
            <Button
              fullWidth
              variant="contained"
              size="large"
              onClick={handleSubmit}
              disabled={loading}
              sx={{
                py: 1.5,
                bgcolor: "white",
                color: "#9e655c",
                fontWeight: 700,
                fontSize: "1.1rem",
                borderRadius: 3,
                textTransform: "none",
                "&:hover": {
                  bgcolor: "#f5f5f5",
                  transform: "translateY(-2px)",
                },
                "&:disabled": {
                  bgcolor: "rgba(255,255,255,0.7)",
                  color: "#9e655c",
                },
                transition: "all 0.3s ease",
              }}
            >
              {loading ? (
                <CircularProgress size={24} sx={{ color: "#9e655c" }} />
              ) : (
                "Đặt hàng ngay"
              )}
            </Button>

            <Button
              fullWidth
              variant="outlined"
              onClick={() => navigate("/cart")}
              disabled={loading}
              sx={{
                py: 1.5,
                borderColor: "rgba(255,255,255,0.5)",
                color: "white",
                fontWeight: 600,
                borderRadius: 3,
                textTransform: "none",
                "&:hover": {
                  borderColor: "white",
                  bgcolor: "rgba(255,255,255,0.1)",
                  transform: "translateY(-1px)",
                },
                "&:disabled": {
                  borderColor: "rgba(255,255,255,0.3)",
                  color: "rgba(255,255,255,0.5)",
                },
                transition: "all 0.3s ease",
              }}
            >
              Quay lại giỏ hàng
            </Button>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default CheckoutPage;
