"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "TimeClock", {
  enumerable: true,
  get: function () {
    return _TimeClock.TimeClock;
  }
});
Object.defineProperty(exports, "clockClasses", {
  enumerable: true,
  get: function () {
    return _clockClasses.clockClasses;
  }
});
Object.defineProperty(exports, "clockNumberClasses", {
  enumerable: true,
  get: function () {
    return _clockNumberClasses.clockNumberClasses;
  }
});
Object.defineProperty(exports, "clockPointerClasses", {
  enumerable: true,
  get: function () {
    return _clockPointerClasses.clockPointerClasses;
  }
});
Object.defineProperty(exports, "getTimeClockUtilityClass", {
  enumerable: true,
  get: function () {
    return _timeClockClasses.getTimeClockUtilityClass;
  }
});
Object.defineProperty(exports, "timeClockClasses", {
  enumerable: true,
  get: function () {
    return _timeClockClasses.timeClockClasses;
  }
});
var _TimeClock = require("./TimeClock");
var _clockClasses = require("./clockClasses");
var _clockNumberClasses = require("./clockNumberClasses");
var _timeClockClasses = require("./timeClockClasses");
var _clockPointerClasses = require("./clockPointerClasses");