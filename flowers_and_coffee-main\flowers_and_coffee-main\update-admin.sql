-- <PERSON><PERSON>t to create/update admin user
-- Run this in SQL Server Management Studio or similar tool

-- First, check if the user exists
SELECT UserId, FullName, Email, RoleId, IsEmailConfirmed 
FROM Users 
WHERE Email = '<EMAIL>';

-- If user exists, update to admin role and confirm email
UPDATE Users 
SET RoleId = 4, 
    IsEmailConfirmed = 1,
    EmailConfirmationToken = NULL
WHERE Email = '<EMAIL>';

-- Check roles table to see available roles
SELECT * FROM Roles;

-- If Roles table is empty, insert default roles
IF NOT EXISTS (SELECT 1 FROM Roles WHERE RoleId = 1)
BEGIN
    INSERT INTO Roles (RoleId, RoleName) VALUES (1, 'Customer');
END

IF NOT EXISTS (SELECT 1 FROM Roles WHERE RoleId = 2)
BEGIN
    INSERT INTO Roles (RoleId, RoleName) VALUES (2, 'Staff');
END

IF NOT EXISTS (SELECT 1 FROM Roles WHERE RoleId = 4)
BEGIN
    INSERT INTO Roles (RoleId, RoleName) VALUES (4, 'Admin');
END

-- Verify the update
SELECT u.UserId, u.FullName, u.Email, u.RoleId, r.RoleName, u.IsEmailConfirmed
FROM Users u
LEFT JOIN Roles r ON u.RoleId = r.RoleId
WHERE u.Email = '<EMAIL>';

-- Alternative: Create admin user directly if it doesn't exist
-- (Password is BCrypt hash of "Admin123!")
IF NOT EXISTS (SELECT 1 FROM Users WHERE Email = '<EMAIL>')
BEGIN
    INSERT INTO Users (FullName, Email, PasswordHash, Phone, Address, Gender, BirthDate, RoleId, CreatedAt, IsEmailConfirmed)
    VALUES (
        'Admin System',
        '<EMAIL>',
        '$2a$11$8K1p/a0dclsgAMlqMfgzaOCkr6x8/iqd62/WdXDiVxuPkk6bsobaW', -- BCrypt hash of "Admin123!"
        '0123456789',
        'Admin Office',
        'Male',
        '1990-01-01',
        4, -- Admin role
        GETDATE(),
        1 -- Email confirmed
    );
END

-- Final verification
SELECT u.UserId, u.FullName, u.Email, u.RoleId, r.RoleName, u.IsEmailConfirmed, u.CreatedAt
FROM Users u
LEFT JOIN Roles r ON u.RoleId = r.RoleId
WHERE u.Email = '<EMAIL>';
