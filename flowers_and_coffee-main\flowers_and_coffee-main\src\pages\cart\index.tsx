import React, { useState, useEffect } from "react";
import {
  Container,
  Paper,
  Typography,
  Box,
  Grid,
  Card,
  CardMedia,
  CardContent,
  IconButton,
  Button,
  TextField,
  Divider,
  Alert,
  CircularProgress,
} from "@mui/material";
import {
  Add,
  Remove,
  Delete,
  ShoppingCart,
  Payment,
} from "@mui/icons-material";
import { useAppSelector, useAppDispatch } from "../../stores/hooks";
import {
  setLoading,
  setCartItems,
  updateCartItem,
  removeCartItem,
  clearCart,
} from "../../stores/reducers/Cart";
import { cartAPI } from "../../services/api";
import { toast } from "react-toastify";
import { useNavigate } from "react-router-dom";

const CartPage = () => {
  const { items, totalItems, totalAmount, loading } = useAppSelector((state) => state.Cart);
  const { isAuthenticated } = useAppSelector((state) => state.Authentication);
  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  const [quantities, setQuantities] = useState<{ [key: number]: number }>({});

  useEffect(() => {
    if (isAuthenticated) {
      loadCart();
    }
  }, [isAuthenticated]);

  useEffect(() => {
    // Initialize quantities from cart items
    const initialQuantities: { [key: number]: number } = {};
    items.forEach(item => {
      initialQuantities[item.productId] = item.quantity;
    });
    setQuantities(initialQuantities);
  }, [items]);

  const loadCart = async () => {
    try {
      dispatch(setLoading(true));
      console.log('Loading cart...');

      // Check if user is authenticated
      if (!isAuthenticated) {
        console.log('User not authenticated, clearing cart');
        dispatch(clearCart());
        return;
      }

      // Clear current cart state first
      dispatch(clearCart());

      const cartData = await cartAPI.getCart();
      console.log('Raw cart data from API:', cartData);

      if (Array.isArray(cartData) && cartData.length > 0) {
        dispatch(setCartItems(cartData));
      } else {
        console.log('No cart items found');
        dispatch(setCartItems([]));
      }
    } catch (error: any) {
      console.error('Error loading cart:', error);

      // If it's an auth error, redirect to login
      if (error.message.includes('500') || error.message.includes('User ID claim not found')) {
        console.log('Authentication error, clearing cart and redirecting to login');
        dispatch(clearCart());
        toast.error('Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.');
        navigate('/auth/login');
      } else {
        toast.error(`Không thể tải giỏ hàng: ${error.message}`);
        dispatch(setCartItems([]));
      }
    } finally {
      dispatch(setLoading(false));
    }
  };

  const handleQuantityChange = async (productId: number, newQuantity: number) => {
    if (newQuantity < 1) return;

    // Update local state immediately for better UX
    setQuantities(prev => ({
      ...prev,
      [productId]: newQuantity
    }));

    try {
      console.log('Updating quantity for product', productId, 'to', newQuantity);

      // Update quantity on backend using updateItem
      await cartAPI.updateItem(productId, newQuantity);

      // Reload cart to get accurate data
      const cartData = await cartAPI.getCart();
      dispatch(setCartItems(cartData));

      console.log('Quantity updated successfully');

    } catch (error: any) {
      console.error('Error updating cart quantity:', error);
      toast.error(`Không thể cập nhật số lượng: ${error.message}`);

      // Revert local state on error
      loadCart();
    }
  };

  const handleRemoveItem = async (productId: number) => {
    try {
      await cartAPI.removeItem(productId);

      // Reload cart to get accurate data
      const cartData = await cartAPI.getCart();
      dispatch(setCartItems(cartData));

      toast.success("Đã xóa sản phẩm khỏi giỏ hàng");
    } catch (error: any) {
      console.error('Error removing cart item:', error);
      toast.error(`Không thể xóa sản phẩm: ${error.message}`);
    }
  };

  const handleClearCart = async () => {
    try {
      // Remove all items from backend
      for (const item of items) {
        await cartAPI.removeItem(item.productId);
      }

      // Clear Redux store
      dispatch(clearCart());

      // Reset local quantities state
      setQuantities({});

      toast.success("Đã xóa tất cả sản phẩm");
    } catch (error: any) {
      console.error('Error clearing cart:', error);
      toast.error(`Không thể xóa giỏ hàng: ${error.message}`);
    }
  };

  const handleResetCart = () => {
    // Emergency reset - clear all cart data
    dispatch(clearCart());
    setQuantities({});
    localStorage.removeItem('persist:root'); // Clear persisted state
    toast.success("Đã reset giỏ hàng");
    window.location.reload(); // Reload to ensure clean state
  };

  const handleCheckout = () => {
    if (items.length === 0) {
      toast.warning("Giỏ hàng trống");
      return;
    }
    navigate("/checkout");
  };

  if (!isAuthenticated) {
    return (
      <Container maxWidth="md" sx={{ mt: 4 }}>
        <Alert severity="warning">
          Vui lòng đăng nhập để xem giỏ hàng
        </Alert>
      </Container>
    );
  }

  if (loading) {
    return (
      <Container maxWidth="md" sx={{ mt: 4, textAlign: "center" }}>
        <CircularProgress />
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {/* Header */}
      <Paper
        elevation={0}
        sx={{
          p: 4,
          mb: 4,
          background: "linear-gradient(135deg, #9e655c 0%, #b8766b 100%)",
          color: "white",
          borderRadius: 4,
          position: "relative",
          overflow: "hidden",
          "&::before": {
            content: '""',
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: "url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><circle cx=\"20\" cy=\"20\" r=\"2\" fill=\"rgba(255,255,255,0.1)\"/><circle cx=\"80\" cy=\"80\" r=\"2\" fill=\"rgba(255,255,255,0.1)\"/><circle cx=\"40\" cy=\"60\" r=\"1\" fill=\"rgba(255,255,255,0.1)\"/></svg>')",
            opacity: 0.3,
          },
        }}
      >
        <Box display="flex" justifyContent="space-between" alignItems="center" sx={{ position: "relative", zIndex: 1 }}>
          <Box display="flex" alignItems="center">
            <Box
              sx={{
                bgcolor: "rgba(255,255,255,0.2)",
                borderRadius: "50%",
                p: 1.5,
                mr: 3,
              }}
            >
              <ShoppingCart sx={{ fontSize: 32, color: "white" }} />
            </Box>
            <Box>
              <Typography variant="h4" fontWeight="bold" sx={{ mb: 0.5 }}>
                Giỏ hàng của bạn
              </Typography>
              <Typography variant="body1" sx={{ opacity: 0.9, fontSize: "1.1rem" }}>
                {items.length > 0 ? `${totalItems} sản phẩm` : "Chưa có sản phẩm nào"}
              </Typography>
            </Box>
          </Box>
          <Button
            variant="outlined"
            onClick={handleResetCart}
            size="small"
            sx={{
              borderColor: "rgba(255,255,255,0.5)",
              color: "white",
              borderRadius: 3,
              px: 2,
              py: 1,
              fontWeight: 600,
              "&:hover": {
                borderColor: "white",
                bgcolor: "rgba(255,255,255,0.15)",
                transform: "translateY(-1px)",
              },
              transition: "all 0.3s ease",
            }}
          >
            Reset Giỏ Hàng
          </Button>
        </Box>
      </Paper>

      {items.length === 0 ? (
        <Paper
          elevation={0}
          sx={{
            p: 6,
            textAlign: "center",
            borderRadius: 4,
            background: "linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)",
            border: "1px solid rgba(158, 101, 92, 0.1)",
          }}
        >
          <Box
            sx={{
              bgcolor: "rgba(158, 101, 92, 0.1)",
              borderRadius: "50%",
              width: 120,
              height: 120,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              mx: "auto",
              mb: 3,
            }}
          >
            <ShoppingCart sx={{ fontSize: 60, color: "#9e655c" }} />
          </Box>
          <Typography variant="h5" sx={{ color: "#9e655c", fontWeight: 600, mb: 2 }}>
            Giỏ hàng trống
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 4, maxWidth: 400, mx: "auto" }}>
            Hãy khám phá bộ sưu tập hoa tươi và đồ uống tuyệt vời của chúng tôi để thêm vào giỏ hàng
          </Typography>
          <Box sx={{ display: "flex", gap: 2, justifyContent: "center", flexWrap: "wrap" }}>
            <Button
              variant="contained"
              size="large"
              onClick={() => navigate("/flower")}
              sx={{
                background: "linear-gradient(135deg, #9e655c 0%, #b8766b 100%)",
                borderRadius: 3,
                px: 4,
                py: 1.5,
                fontWeight: 600,
                textTransform: "none",
                "&:hover": {
                  background: "linear-gradient(135deg, #8a5650 0%, #a66b60 100%)",
                  transform: "translateY(-2px)",
                },
                transition: "all 0.3s ease",
              }}
            >
              Mua hoa tươi
            </Button>
            <Button
              variant="outlined"
              size="large"
              onClick={() => navigate("/drink")}
              sx={{
                borderColor: "#9e655c",
                color: "#9e655c",
                borderRadius: 3,
                px: 4,
                py: 1.5,
                fontWeight: 600,
                textTransform: "none",
                "&:hover": {
                  borderColor: "#8a5650",
                  bgcolor: "rgba(158, 101, 92, 0.05)",
                  transform: "translateY(-2px)",
                },
                transition: "all 0.3s ease",
              }}
            >
              Mua đồ uống
            </Button>
          </Box>
        </Paper>
      ) : (
        <Grid container spacing={4}>
          <Grid item xs={12} md={8}>
            <Paper
              elevation={0}
              sx={{
                p: 3,
                borderRadius: 4,
                background: "linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)",
                border: "1px solid rgba(158, 101, 92, 0.1)",
              }}
            >
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
                <Typography variant="h6" sx={{ color: "#9e655c", fontWeight: 600 }}>
                  Sản phẩm ({totalItems} món)
                </Typography>
                <Button
                  variant="outlined"
                  color="error"
                  size="small"
                  onClick={handleClearCart}
                  sx={{
                    borderRadius: 3,
                    px: 2,
                    py: 0.5,
                    fontWeight: 600,
                    textTransform: "none",
                    "&:hover": {
                      transform: "translateY(-1px)",
                    },
                    transition: "all 0.3s ease",
                  }}
                >
                  Xóa tất cả
                </Button>
              </Box>

              {items.map((item) => (
                <Card
                  key={item.id}
                  sx={{
                    mb: 3,
                    display: "flex",
                    borderRadius: 4,
                    border: "1px solid rgba(158, 101, 92, 0.1)",
                    background: "linear-gradient(135deg, #ffffff 0%, #fefefe 100%)",
                    transition: "all 0.3s ease",
                    overflow: "hidden",
                    "&:hover": {
                      boxShadow: "0 12px 24px rgba(158, 101, 92, 0.15)",
                      border: "1px solid rgba(158, 101, 92, 0.2)",
                      transform: "translateY(-2px)",
                    },
                  }}
                >
                  <Box sx={{ position: "relative" }}>
                    <CardMedia
                      component="img"
                      sx={{
                        width: 140,
                        height: 140,
                        objectFit: "cover",
                      }}
                      image={item.product?.imageUrl || "/placeholder.jpg"}
                      alt={item.product?.name}
                    />
                    <Box
                      sx={{
                        position: "absolute",
                        top: 8,
                        left: 8,
                        bgcolor: "rgba(158, 101, 92, 0.9)",
                        color: "white",
                        borderRadius: 2,
                        px: 1,
                        py: 0.5,
                        fontSize: "0.75rem",
                        fontWeight: 600,
                      }}
                    >
                      #{item.id}
                    </Box>
                  </Box>
                  <CardContent sx={{ flex: 1, p: 3, display: "flex", flexDirection: "column", justifyContent: "space-between" }}>
                    <Box>
                      <Typography
                        variant="h6"
                        gutterBottom
                        sx={{
                          fontWeight: 700,
                          color: "#2c3e50",
                          fontSize: "1.2rem",
                          mb: 1,
                        }}
                      >
                        {item.product?.name}
                      </Typography>
                      <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{
                          mb: 2,
                          lineHeight: 1.6,
                          display: "-webkit-box",
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: "vertical",
                          overflow: "hidden",
                        }}
                      >
                        {item.product?.description || "Sản phẩm chất lượng cao, được chọn lọc kỹ càng"}
                      </Typography>
                    </Box>
                    <Box sx={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
                      <Typography
                        variant="h6"
                        sx={{
                          color: "#9e655c",
                          fontWeight: 700,
                          fontSize: "1.3rem",
                        }}
                      >
                        {item.product?.price?.toLocaleString("vi-VN")}đ
                      </Typography>
                      <Typography
                        variant="body2"
                        sx={{
                          color: "#9e655c",
                          fontWeight: 600,
                          bgcolor: "rgba(158, 101, 92, 0.1)",
                          px: 2,
                          py: 0.5,
                          borderRadius: 2,
                        }}
                      >
                        Tổng: {((item.product?.price || 0) * (quantities[item.productId] || item.quantity)).toLocaleString("vi-VN")}đ
                      </Typography>
                    </Box>
                  </CardContent>
                  <Box
                    sx={{
                      p: 3,
                      display: "flex",
                      flexDirection: "column",
                      justifyContent: "space-between",
                      alignItems: "center",
                      minWidth: 140,
                      bgcolor: "rgba(158, 101, 92, 0.02)",
                    }}
                  >
                    {/* Quantity Controls */}
                    <Box
                      display="flex"
                      alignItems="center"
                      sx={{
                        bgcolor: "rgba(158, 101, 92, 0.1)",
                        borderRadius: 3,
                        p: 1,
                        mb: 3,
                        border: "1px solid rgba(158, 101, 92, 0.2)",
                      }}
                    >
                      <IconButton
                        size="small"
                        onClick={() => {
                          const currentQty = quantities[item.productId] || item.quantity;
                          handleQuantityChange(item.productId, Math.max(1, currentQty - 1));
                        }}
                        disabled={(quantities[item.productId] || item.quantity) <= 1}
                        sx={{
                          bgcolor: "white",
                          width: 32,
                          height: 32,
                          "&:hover": {
                            bgcolor: "#f0f0f0",
                            transform: "scale(1.1)",
                          },
                          "&.Mui-disabled": {
                            bgcolor: "#f9f9f9",
                            color: "#ccc",
                          },
                          transition: "all 0.2s ease",
                        }}
                      >
                        <Remove fontSize="small" />
                      </IconButton>
                      <TextField
                        size="small"
                        value={quantities[item.productId] || item.quantity}
                        onChange={(e) => {
                          const value = parseInt(e.target.value) || 1;
                          if (value >= 1) {
                            handleQuantityChange(item.productId, value);
                          }
                        }}
                        sx={{
                          width: 60,
                          mx: 1,
                          "& .MuiOutlinedInput-root": {
                            bgcolor: "white",
                            borderRadius: 2,
                            "& fieldset": {
                              border: "1px solid rgba(158, 101, 92, 0.3)",
                            },
                            "&:hover fieldset": {
                              border: "1px solid rgba(158, 101, 92, 0.5)",
                            },
                            "&.Mui-focused fieldset": {
                              border: "2px solid #9e655c",
                            },
                          },
                        }}
                        inputProps={{
                          min: 1,
                          style: {
                            textAlign: "center",
                            fontWeight: 700,
                            padding: "8px 0",
                            color: "#9e655c",
                          }
                        }}
                        type="number"
                      />
                      <IconButton
                        size="small"
                        onClick={() => {
                          const currentQty = quantities[item.productId] || item.quantity;
                          handleQuantityChange(item.productId, currentQty + 1);
                        }}
                        sx={{
                          bgcolor: "white",
                          width: 32,
                          height: 32,
                          "&:hover": {
                            bgcolor: "#f0f0f0",
                            transform: "scale(1.1)",
                          },
                          transition: "all 0.2s ease",
                        }}
                      >
                        <Add fontSize="small" />
                      </IconButton>
                    </Box>

                    {/* Delete Button */}
                    <IconButton
                      color="error"
                      onClick={() => handleRemoveItem(item.productId)}
                      sx={{
                        bgcolor: "#ffebee",
                        width: 44,
                        height: 44,
                        border: "2px solid #ffcdd2",
                        "&:hover": {
                          bgcolor: "#ffcdd2",
                          transform: "scale(1.1) rotate(5deg)",
                          border: "2px solid #ef5350",
                        },
                        transition: "all 0.3s ease",
                      }}
                    >
                      <Delete fontSize="medium" />
                    </IconButton>
                  </Box>
                </Card>
              ))}
            </Paper>
          </Grid>

          <Grid item xs={12} md={4}>
            <Paper
              elevation={0}
              sx={{
                p: 4,
                position: "sticky",
                top: 20,
                borderRadius: 4,
                background: "linear-gradient(135deg, #9e655c 0%, #b8766b 100%)",
                color: "white",
                border: "1px solid rgba(158, 101, 92, 0.2)",
              }}
            >
              <Typography variant="h5" gutterBottom sx={{ fontWeight: 700, mb: 3 }}>
                Tóm tắt đơn hàng
              </Typography>

              <Box
                sx={{
                  bgcolor: "rgba(255,255,255,0.1)",
                  borderRadius: 3,
                  p: 2,
                  mb: 3,
                  backdropFilter: "blur(10px)",
                }}
              >
                <Box display="flex" justifyContent="space-between" mb={2}>
                  <Typography sx={{ opacity: 0.9 }}>Tạm tính:</Typography>
                  <Typography sx={{ fontWeight: 600 }}>{totalAmount.toLocaleString("vi-VN")}đ</Typography>
                </Box>

                <Box display="flex" justifyContent="space-between" mb={2}>
                  <Typography sx={{ opacity: 0.9 }}>Phí vận chuyển:</Typography>
                  <Typography sx={{ fontWeight: 600, color: "#4caf50" }}>Miễn phí</Typography>
                </Box>

                <Divider sx={{ my: 2, bgcolor: "rgba(255,255,255,0.3)" }} />

                <Box display="flex" justifyContent="space-between" mb={1}>
                  <Typography variant="h6" sx={{ fontWeight: 700 }}>Tổng cộng:</Typography>
                  <Typography variant="h6" sx={{ fontWeight: 700, fontSize: "1.4rem" }}>
                    {totalAmount.toLocaleString("vi-VN")}đ
                  </Typography>
                </Box>
              </Box>

              <Button
                fullWidth
                variant="contained"
                size="large"
                startIcon={<Payment />}
                onClick={handleCheckout}
                sx={{
                  mb: 2,
                  py: 1.5,
                  bgcolor: "white",
                  color: "#9e655c",
                  fontWeight: 700,
                  fontSize: "1.1rem",
                  borderRadius: 3,
                  textTransform: "none",
                  "&:hover": {
                    bgcolor: "#f5f5f5",
                    transform: "translateY(-2px)",
                  },
                  transition: "all 0.3s ease",
                }}
              >
                Thanh toán ngay
              </Button>

              <Button
                fullWidth
                variant="outlined"
                onClick={() => navigate("/flower")}
                sx={{
                  py: 1.5,
                  borderColor: "rgba(255,255,255,0.5)",
                  color: "white",
                  fontWeight: 600,
                  borderRadius: 3,
                  textTransform: "none",
                  "&:hover": {
                    borderColor: "white",
                    bgcolor: "rgba(255,255,255,0.1)",
                    transform: "translateY(-1px)",
                  },
                  transition: "all 0.3s ease",
                }}
              >
                Tiếp tục mua sắm
              </Button>
            </Paper>
          </Grid>
        </Grid>
      )}
    </Container>
  );
};

export default CartPage;
