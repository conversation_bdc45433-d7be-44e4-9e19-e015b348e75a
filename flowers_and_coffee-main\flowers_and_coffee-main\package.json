{"name": "hope-application", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fontsource/roboto": "^5.2.5", "@mui/icons-material": "^7.0.2", "@mui/material": "^7.0.2", "@mui/styled-engine-sc": "^7.0.2", "@mui/x-date-pickers": "^8.5.0", "@reduxjs/toolkit": "^2.7.0", "dayjs": "^1.11.13", "react": "^19.0.0", "react-dom": "^19.0.0", "react-redux": "^9.2.0", "react-router-dom": "^7.5.1", "react-toastify": "^11.0.5", "recharts": "^2.15.3", "redux-persist": "^6.0.0", "sass": "^1.87.0", "styled-components": "^6.1.17"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/react-router-dom": "^5.3.3", "@types/react-toastify": "^4.1.0", "@types/redux-persist": "^4.3.1", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vite": "^6.3.1"}}