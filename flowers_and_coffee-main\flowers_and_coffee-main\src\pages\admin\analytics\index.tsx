import React, { useState, useEffect } from "react";
import {
  Box,
  Typography,
  Grid,
  Paper,
  Card,
  CardContent,
  CircularProgress,
  Alert,
  TextField,
  MenuItem,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Divider,
  Container,
  Fade,
  Grow,
} from "@mui/material";
import {
  TrendingUp,
  TrendingDown,
  Assessment,
  Timeline,
  AttachMoney,
  ShoppingCart,
  People,
  LocalShipping,
  CalendarToday,
  FilterList,
} from "@mui/icons-material";
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";
import { revenueAPI } from "../../../services/api";
import { DailyRevenue, MonthlyRevenue, RevenueStats, TopProduct, OrderStatus } from "../../../types";
import { toast } from "react-toastify";

const AdminAnalytics: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [dateRange, setDateRange] = useState("30days");
  const [chartType, setChartType] = useState("daily");

  // State for data
  const [revenueStats, setRevenueStats] = useState<RevenueStats | null>(null);
  const [dailyRevenue, setDailyRevenue] = useState<DailyRevenue[]>([]);
  const [monthlyRevenue, setMonthlyRevenue] = useState<MonthlyRevenue[]>([]);
  const [topProducts, setTopProducts] = useState<TopProduct[]>([]);
  const [orderStatusStats, setOrderStatusStats] = useState<OrderStatus[]>([]);

  // Fetch data
  const fetchData = async () => {
    try {
      setLoading(true);
      setError("");

      const endDate = new Date();
      const startDate = new Date();

      // Calculate date range
      switch (dateRange) {
        case "7days":
          startDate.setDate(endDate.getDate() - 7);
          break;
        case "30days":
          startDate.setDate(endDate.getDate() - 30);
          break;
        case "90days":
          startDate.setDate(endDate.getDate() - 90);
          break;
        default:
          startDate.setDate(endDate.getDate() - 30);
      }

      const [statsRes, dailyRes, monthlyRes, topProductsRes, statusRes] = await Promise.all([
        revenueAPI.getRevenueStats(),
        revenueAPI.getDailyRevenue(startDate.toISOString().split('T')[0], endDate.toISOString().split('T')[0]),
        revenueAPI.getMonthlyRevenue(endDate.getFullYear()),
        revenueAPI.getTopProducts(10, startDate.toISOString().split('T')[0], endDate.toISOString().split('T')[0]),
        revenueAPI.getOrderStatusStats(startDate.toISOString().split('T')[0], endDate.toISOString().split('T')[0])
      ]);

      setRevenueStats(statsRes);
      setDailyRevenue(dailyRes);
      setMonthlyRevenue(monthlyRes);
      setTopProducts(topProductsRes);
      setOrderStatusStats(statusRes);

    } catch (error: any) {
      console.error('Error fetching analytics data:', error);
      setError(error.message || "Có lỗi xảy ra khi tải dữ liệu");
      toast.error("Không thể tải dữ liệu thống kê");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [dateRange]);

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN');
  };

  if (loading) {
    return (
      <Box 
        display="flex" 
        flexDirection="column"
        justifyContent="center" 
        alignItems="center" 
        minHeight="100vh"
        sx={{
          background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
        }}
      >
        <CircularProgress 
          size={80} 
          sx={{ 
            color: "#fff",
            mb: 3,
            '& .MuiCircularProgress-circle': {
              strokeLinecap: 'round',
            }
          }} 
        />
        <Typography variant="h6" sx={{ color: "#fff", fontWeight: 500 }}>
          Đang tải dữ liệu thống kê...
        </Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Container maxWidth="md" sx={{ mt: 4 }}>
        <Alert 
          severity="error" 
          sx={{ 
            borderRadius: 3,
            boxShadow: "0 8px 32px rgba(244, 67, 54, 0.2)"
          }}
        >
          {error}
        </Alert>
      </Container>
    );
  }

  return (
    <Box sx={{
      minHeight: "100vh",
      background: "linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)",
      py: 4
    }}>
      <Container maxWidth="xl">
        <Fade in timeout={800}>
          <Box>
            {/* Header */}
            <Box
              display="flex"
              flexDirection={{ xs: "column", lg: "row" }}
              justifyContent="space-between"
              alignItems={{ xs: "flex-start", lg: "center" }}
              mb={5}
              gap={3}
            >
              <Box>
                <Typography
                  variant="h3"
                  sx={{
                    fontWeight: 800,
                    background: "linear-gradient(45deg, #667eea 30%, #764ba2 90%)",
                    backgroundClip: "text",
                    WebkitBackgroundClip: "text",
                    WebkitTextFillColor: "transparent",
                    display: "flex",
                    alignItems: "center",
                    gap: 2,
                    fontSize: { xs: "2rem", md: "2.5rem" },
                    mb: 1
                  }}
                >
                  <Assessment sx={{ fontSize: { xs: 32, md: 40 }, color: "#667eea" }} />
                  Thống kê Doanh thu
                </Typography>
                <Typography variant="h6" sx={{ color: "#64748b", fontWeight: 400 }}>
                  Tổng quan hiệu suất kinh doanh của bạn
                </Typography>
              </Box>

              <Box display="flex" gap={2} flexWrap="wrap">
                <TextField
                  select
                  size="small"
                  value={dateRange}
                  onChange={(e) => setDateRange(e.target.value)}
                  sx={{
                    minWidth: 140,
                    "& .MuiOutlinedInput-root": {
                      borderRadius: 3,
                      bgcolor: "rgba(255, 255, 255, 0.9)",
                      backdropFilter: "blur(10px)",
                      border: "1px solid rgba(102, 126, 234, 0.2)",
                      "&:hover": {
                        border: "1px solid rgba(102, 126, 234, 0.4)",
                      },
                      "&.Mui-focused": {
                        border: "2px solid #667eea",
                        boxShadow: "0 0 0 3px rgba(102, 126, 234, 0.1)"
                      }
                    }
                  }}
                  InputProps={{
                    startAdornment: <CalendarToday sx={{ mr: 1, fontSize: 18, color: "#667eea" }} />
                  }}
                >
                  <MenuItem value="7days">7 ngày qua</MenuItem>
                  <MenuItem value="30days">30 ngày qua</MenuItem>
                  <MenuItem value="90days">90 ngày qua</MenuItem>
                </TextField>

                <TextField
                  select
                  size="small"
                  value={chartType}
                  onChange={(e) => setChartType(e.target.value)}
                  sx={{
                    minWidth: 140,
                    "& .MuiOutlinedInput-root": {
                      borderRadius: 3,
                      bgcolor: "rgba(255, 255, 255, 0.9)",
                      backdropFilter: "blur(10px)",
                      border: "1px solid rgba(102, 126, 234, 0.2)",
                      "&:hover": {
                        border: "1px solid rgba(102, 126, 234, 0.4)",
                      },
                      "&.Mui-focused": {
                        border: "2px solid #667eea",
                        boxShadow: "0 0 0 3px rgba(102, 126, 234, 0.1)"
                      }
                    }
                  }}
                  InputProps={{
                    startAdornment: <FilterList sx={{ mr: 1, fontSize: 18, color: "#667eea" }} />
                  }}
                >
                  <MenuItem value="daily">Theo ngày</MenuItem>
                  <MenuItem value="monthly">Theo tháng</MenuItem>
                </TextField>
              </Box>
            </Box>

            {/* Stats Cards */}
            {revenueStats && (
              <Grid container spacing={3} sx={{ mb: 5 }}>
                <Grid item xs={12} sm={6} lg={3}>
                  <Grow in timeout={600}>
                    <Card
                      sx={{
                        borderRadius: 4,
                        background: "linear-gradient(135deg, #4ade80 0%, #22c55e 100%)",
                        color: "white",
                        transition: "all 0.4s cubic-bezier(0.4, 0, 0.2, 1)",
                        cursor: "pointer",
                        position: "relative",
                        overflow: "hidden",
                        "&:hover": {
                          transform: "translateY(-12px) scale(1.02)",
                          boxShadow: "0 25px 50px rgba(34, 197, 94, 0.4)"
                        },
                        "&::before": {
                          content: '""',
                          position: "absolute",
                          top: 0,
                          right: 0,
                          width: "100px",
                          height: "100px",
                          background: "rgba(255,255,255,0.1)",
                          borderRadius: "50%",
                          transform: "translate(30px, -30px)"
                        }
                      }}
                    >
                      <CardContent sx={{ p: 4, position: "relative", zIndex: 1 }}>
                        <Box display="flex" alignItems="center" justifyContent="space-between" mb={3}>
                          <Box
                            sx={{
                              p: 2,
                              borderRadius: 3,
                              bgcolor: "rgba(255,255,255,0.2)",
                              backdropFilter: "blur(10px)"
                            }}
                          >
                            <AttachMoney sx={{ fontSize: 28 }} />
                          </Box>
                          <Box textAlign="right">
                            <Typography variant="h4" sx={{ fontWeight: 800, mb: 0.5 }}>
                              {formatCurrency(revenueStats.thisMonthRevenue)}
                            </Typography>
                          </Box>
                        </Box>
                        <Typography variant="body2" sx={{ opacity: 0.9, mb: 2, fontSize: "0.95rem" }}>
                          Doanh thu tháng này
                        </Typography>
                        <Box display="flex" alignItems="center">
                          <Chip
                            icon={revenueStats.monthlyGrowthPercentage >= 0 ? <TrendingUp /> : <TrendingDown />}
                            label={`${revenueStats.monthlyGrowthPercentage.toFixed(1)}%`}
                            size="small"
                            sx={{
                              bgcolor: "rgba(255,255,255,0.2)",
                              color: "white",
                              fontWeight: 700,
                              "& .MuiChip-icon": { color: "white" }
                            }}
                          />
                          <Typography variant="caption" sx={{ ml: 1, opacity: 0.8 }}>
                            so với tháng trước
                          </Typography>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grow>
                </Grid>

                <Grid item xs={12} sm={6} lg={3}>
                  <Grow in timeout={800}>
                    <Card
                      sx={{
                        borderRadius: 4,
                        background: "linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)",
                        color: "white",
                        transition: "all 0.4s cubic-bezier(0.4, 0, 0.2, 1)",
                        cursor: "pointer",
                        position: "relative",
                        overflow: "hidden",
                        "&:hover": {
                          transform: "translateY(-12px) scale(1.02)",
                          boxShadow: "0 25px 50px rgba(59, 130, 246, 0.4)"
                        },
                        "&::before": {
                          content: '""',
                          position: "absolute",
                          top: 0,
                          right: 0,
                          width: "100px",
                          height: "100px",
                          background: "rgba(255,255,255,0.1)",
                          borderRadius: "50%",
                          transform: "translate(30px, -30px)"
                        }
                      }}
                    >
                      <CardContent sx={{ p: 4, position: "relative", zIndex: 1 }}>
                        <Box display="flex" alignItems="center" justifyContent="space-between" mb={3}>
                          <Box
                            sx={{
                              p: 2,
                              borderRadius: 3,
                              bgcolor: "rgba(255,255,255,0.2)",
                              backdropFilter: "blur(10px)"
                            }}
                          >
                            <ShoppingCart sx={{ fontSize: 28 }} />
                          </Box>
                          <Box textAlign="right">
                            <Typography variant="h4" sx={{ fontWeight: 800, mb: 0.5 }}>
                              {revenueStats.thisMonthOrders}
                            </Typography>
                          </Box>
                        </Box>
                        <Typography variant="body2" sx={{ opacity: 0.9, mb: 2, fontSize: "0.95rem" }}>
                          Đơn hàng tháng này
                        </Typography>
                        <Chip
                          label={`Hoàn thành: ${revenueStats.completedOrders}`}
                          size="small"
                          sx={{
                            bgcolor: "rgba(255,255,255,0.2)",
                            color: "white",
                            fontWeight: 700
                          }}
                        />
                      </CardContent>
                    </Card>
                  </Grow>
                </Grid>

                <Grid item xs={12} sm={6} lg={3}>
                  <Grow in timeout={1000}>
                    <Card
                      sx={{
                        borderRadius: 4,
                        background: "linear-gradient(135deg, #f59e0b 0%, #d97706 100%)",
                        color: "white",
                        transition: "all 0.4s cubic-bezier(0.4, 0, 0.2, 1)",
                        cursor: "pointer",
                        position: "relative",
                        overflow: "hidden",
                        "&:hover": {
                          transform: "translateY(-12px) scale(1.02)",
                          boxShadow: "0 25px 50px rgba(245, 158, 11, 0.4)"
                        },
                        "&::before": {
                          content: '""',
                          position: "absolute",
                          top: 0,
                          right: 0,
                          width: "100px",
                          height: "100px",
                          background: "rgba(255,255,255,0.1)",
                          borderRadius: "50%",
                          transform: "translate(30px, -30px)"
                        }
                      }}
                    >
                      <CardContent sx={{ p: 4, position: "relative", zIndex: 1 }}>
                        <Box display="flex" alignItems="center" justifyContent="space-between" mb={3}>
                          <Box
                            sx={{
                              p: 2,
                              borderRadius: 3,
                              bgcolor: "rgba(255,255,255,0.2)",
                              backdropFilter: "blur(10px)"
                            }}
                          >
                            <Timeline sx={{ fontSize: 28 }} />
                          </Box>
                          <Box textAlign="right">
                            <Typography variant="h4" sx={{ fontWeight: 800, mb: 0.5 }}>
                              {formatCurrency(revenueStats.averageOrderValue)}
                            </Typography>
                          </Box>
                        </Box>
                        <Typography variant="body2" sx={{ opacity: 0.9, mb: 2, fontSize: "0.95rem" }}>
                          Giá trị đơn hàng trung bình
                        </Typography>
                        <Chip
                          label={`Hôm nay: ${formatCurrency(revenueStats.todayRevenue)}`}
                          size="small"
                          sx={{
                            bgcolor: "rgba(255,255,255,0.2)",
                            color: "white",
                            fontWeight: 700
                          }}
                        />
                      </CardContent>
                    </Card>
                  </Grow>
                </Grid>

                <Grid item xs={12} sm={6} lg={3}>
                  <Grow in timeout={1200}>
                    <Card
                      sx={{
                        borderRadius: 4,
                        background: "linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)",
                        color: "white",
                        transition: "all 0.4s cubic-bezier(0.4, 0, 0.2, 1)",
                        cursor: "pointer",
                        position: "relative",
                        overflow: "hidden",
                        "&:hover": {
                          transform: "translateY(-12px) scale(1.02)",
                          boxShadow: "0 25px 50px rgba(139, 92, 246, 0.4)"
                        },
                        "&::before": {
                          content: '""',
                          position: "absolute",
                          top: 0,
                          right: 0,
                          width: "100px",
                          height: "100px",
                          background: "rgba(255,255,255,0.1)",
                          borderRadius: "50%",
                          transform: "translate(30px, -30px)"
                        }
                      }}
                    >
                      <CardContent sx={{ p: 4, position: "relative", zIndex: 1 }}>
                        <Box display="flex" alignItems="center" justifyContent="space-between" mb={3}>
                          <Box
                            sx={{
                              p: 2,
                              borderRadius: 3,
                              bgcolor: "rgba(255,255,255,0.2)",
                              backdropFilter: "blur(10px)"
                            }}
                          >
                            <LocalShipping sx={{ fontSize: 28 }} />
                          </Box>
                          <Box textAlign="right">
                            <Typography variant="h4" sx={{ fontWeight: 800, mb: 0.5 }}>
                              {revenueStats.pendingOrders}
                            </Typography>
                          </Box>
                        </Box>
                        <Typography variant="body2" sx={{ opacity: 0.9, mb: 2, fontSize: "0.95rem" }}>
                          Đơn hàng chờ xử lý
                        </Typography>
                        <Chip
                          label={`Đã hủy: ${revenueStats.cancelledOrders}`}
                          size="small"
                          sx={{
                            bgcolor: "rgba(255,255,255,0.2)",
                            color: "white",
                            fontWeight: 700
                          }}
                        />
                      </CardContent>
                    </Card>
                  </Grow>
                </Grid>
              </Grid>
            )}

            {/* Revenue Chart */}
            <Fade in timeout={1000}>
              <Paper
                sx={{
                  p: 4,
                  borderRadius: 4,
                  mb: 4,
                  background: "rgba(255, 255, 255, 0.95)",
                  backdropFilter: "blur(20px)",
                  border: "1px solid rgba(102, 126, 234, 0.1)",
                  boxShadow: "0 20px 40px rgba(102, 126, 234, 0.1)",
                  transition: "all 0.3s ease",
                  "&:hover": {
                    boxShadow: "0 25px 50px rgba(102, 126, 234, 0.15)"
                  }
                }}
              >
                <Typography
                  variant="h5"
                  sx={{
                    fontWeight: 700,
                    color: "#1e293b",
                    mb: 4,
                    display: "flex",
                    alignItems: "center",
                    gap: 2
                  }}
                >
                  📈 Biểu đồ doanh thu
                </Typography>

                <Box sx={{ height: 400, width: "100%" }}>
                  <ResponsiveContainer width="100%" height="100%">
                    {chartType === "daily" ? (
                      <AreaChart data={dailyRevenue}>
                        <defs>
                          <linearGradient id="colorRevenue" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor="#667eea" stopOpacity={0.8}/>
                            <stop offset="95%" stopColor="#667eea" stopOpacity={0.1}/>
                          </linearGradient>
                        </defs>
                        <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
                        <XAxis
                          dataKey="date"
                          tickFormatter={(value) => formatDate(value)}
                          stroke="#64748b"
                          fontSize={12}
                        />
                        <YAxis
                          tickFormatter={(value) => `${(value / 1000000).toFixed(1)}M`}
                          stroke="#64748b"
                          fontSize={12}
                        />
                        <Tooltip
                          formatter={(value: any) => [formatCurrency(value), "Doanh thu"]}
                          labelFormatter={(label) => `Ngày: ${formatDate(label)}`}
                          contentStyle={{
                            backgroundColor: "rgba(255, 255, 255, 0.98)",
                            border: "1px solid #667eea",
                            borderRadius: "12px",
                            boxShadow: "0 10px 25px rgba(0, 0, 0, 0.1)",
                            backdropFilter: "blur(10px)"
                          }}
                        />
                        <Area
                          type="monotone"
                          dataKey="revenue"
                          stroke="#667eea"
                          strokeWidth={3}
                          fillOpacity={1}
                          fill="url(#colorRevenue)"
                        />
                      </AreaChart>
                    ) : (
                      <BarChart data={monthlyRevenue}>
                        <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
                        <XAxis
                          dataKey="monthName"
                          stroke="#64748b"
                          fontSize={12}
                        />
                        <YAxis
                          tickFormatter={(value) => `${(value / 1000000).toFixed(1)}M`}
                          stroke="#64748b"
                          fontSize={12}
                        />
                        <Tooltip
                          formatter={(value: any) => [formatCurrency(value), "Doanh thu"]}
                          contentStyle={{
                            backgroundColor: "rgba(255, 255, 255, 0.98)",
                            border: "1px solid #667eea",
                            borderRadius: "12px",
                            boxShadow: "0 10px 25px rgba(0, 0, 0, 0.1)",
                            backdropFilter: "blur(10px)"
                          }}
                        />
                        <Bar
                          dataKey="revenue"
                          fill="#667eea"
                          radius={[6, 6, 0, 0]}
                        />
                      </BarChart>
                    )}
                  </ResponsiveContainer>
                </Box>
              </Paper>
            </Fade>

            {/* Bottom Row */}
            <Grid container spacing={4}>
              <Grid item xs={12} lg={6}>
                <Fade in timeout={1200}>
                  <Paper
                    sx={{
                      p: 4,
                      borderRadius: 4,
                      background: "rgba(255, 255, 255, 0.95)",
                      backdropFilter: "blur(20px)",
                      border: "1px solid rgba(102, 126, 234, 0.1)",
                      boxShadow: "0 20px 40px rgba(102, 126, 234, 0.1)",
                      height: "100%",
                      transition: "all 0.3s ease",
                      "&:hover": {
                        boxShadow: "0 25px 50px rgba(102, 126, 234, 0.15)"
                      }
                    }}
                  >
                    <Typography
                      variant="h5"
                      sx={{
                        mb: 3,
                        fontWeight: 700,
                        color: "#1e293b",
                        display: "flex",
                        alignItems: "center",
                        gap: 2
                      }}
                    >
                      🏆 Top sản phẩm bán chạy
                    </Typography>

                    <List sx={{ p: 0 }}>
                      {topProducts.slice(0, 5).map((product, index) => (
                        <Box key={product.productId}>
                          <ListItem 
                            sx={{ 
                              px: 0, 
                              py: 2,
                              borderRadius: 3,
                              mb: 1,
                              transition: "all 0.3s ease",
                              "&:hover": {
                                bgcolor: "rgba(102, 126, 234, 0.05)",
                                transform: "translateX(8px)"
                              }
                            }}
                          >
                            <ListItemAvatar>
                              <Box position="relative">
                                <Avatar
                                  src={product.imageUrl}
                                  alt={product.productName}
                                  sx={{
                                    width: 50,
                                    height: 50,
                                    border: "3px solid #667eea",
                                    boxShadow: "0 4px 12px rgba(102, 126, 234, 0.3)"
                                  }}
                                />
                                <Chip
                                  label={index + 1}
                                  size="small"
                                  sx={{
                                    position: "absolute",
                                    top: -8,
                                    right: -8,
                                    bgcolor: index === 0 ? "#ffd700" : index === 1 ? "#c0c0c0" : index === 2 ? "#cd7f32" : "#e2e8f0",
                                    color: index < 3 ? "#fff" : "#64748b",
                                    fontWeight: 800,
                                    minWidth: 24,
                                    height: 24
                                  }}
                                />
                              </Box>
                            </ListItemAvatar>
                            <ListItemText
                              primary={
                                <Typography variant="body1" sx={{ fontWeight: 600, color: "#1e293b" }}>
                                  {product.productName}
                                </Typography>
                              }
                              secondary={
                                <Box mt={0.5}>
                                  <Typography variant="body2" color="text.secondary">
                                    Đã bán: {product.totalSold} sản phẩm
                                  </Typography>
                                  <Typography variant="body2" sx={{ color: "#22c55e", fontWeight: 600, mt: 0.5 }}>
                                    {formatCurrency(product.totalRevenue)}
                                  </Typography>
                                </Box>
                              }
                            />
                          </ListItem>
                          {index < topProducts.slice(0, 5).length - 1 && (
                            <Divider sx={{ bgcolor: "rgba(102, 126, 234, 0.1)" }} />
                          )}
                        </Box>
                      ))}
                    </List>
                  </Paper>
                </Fade>
              </Grid>

              <Grid item xs={12} lg={6}>
                <Fade in timeout={1400}>
                  <Paper
                    sx={{
                      p: 4,
                      borderRadius: 4,
                      background: "rgba(255, 255, 255, 0.95)",
                      backdropFilter: "blur(20px)",
                      border: "1px solid rgba(102, 126, 234, 0.1)",
                      boxShadow: "0 20px 40px rgba(102, 126, 234, 0.1)",
                      height: "100%",
                      transition: "all 0.3s ease",
                      "&:hover": {
                        boxShadow: "0 25px 50px rgba(102, 126, 234, 0.15)"
                      }
                    }}
                  >
                    <Typography
                      variant="h5"
                      sx={{
                        mb: 3,
                        fontWeight: 700,
                        color: "#1e293b",
                        display: "flex",
                        alignItems: "center",
                        gap: 2
                      }}
                    >
                      📊 Trạng thái đơn hàng
                    </Typography>

                    <Box sx={{ height: 280, mb: 3 }}>
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={orderStatusStats}
                            cx="50%"
                            cy="50%"
                            innerRadius={60}
                            outerRadius={110}
                            paddingAngle={5}
                            dataKey="count"
                          >
                            {orderStatusStats.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={entry.color} />
                            ))}
                          </Pie>
                          <Tooltip
                            formatter={(value: any, name) => [`${value} đơn`, "Số lượng"]}
                            contentStyle={{
                              backgroundColor: "rgba(255, 255, 255, 0.98)",
                              border: "1px solid #667eea",
                              borderRadius: "12px",
                              boxShadow: "0 10px 25px rgba(0, 0, 0, 0.1)",
                              backdropFilter: "blur(10px)"
                            }}
                          />
                        </PieChart>
                      </ResponsiveContainer>
                    </Box>

                    <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
                      {orderStatusStats.map((status) => (
                        <Box 
                          key={status.status} 
                          display="flex" 
                          alignItems="center" 
                          justifyContent="space-between"
                          sx={{
                            p: 2,
                            borderRadius: 2,
                            bgcolor: "rgba(102, 126, 234, 0.05)",
                            transition: "all 0.3s ease",
                            "&:hover": {
                              bgcolor: "rgba(102, 126, 234, 0.1)",
                              transform: "translateX(4px)"
                            }
                          }}
                        >
                          <Box display="flex" alignItems="center" gap={2}>
                            <Box
                              sx={{
                                width: 16,
                                height: 16,
                                borderRadius: "50%",
                                bgcolor: status.color,
                                boxShadow: `0 2px 8px ${status.color}40`
                              }}
                            />
                            <Typography variant="body1" sx={{ fontWeight: 600, color: "#1e293b" }}>
                              {status.status}
                            </Typography>
                          </Box>
                          <Box textAlign="right">
                            <Typography variant="body1" sx={{ fontWeight: 700, color: "#1e293b" }}>
                              {status.count}
                            </Typography>
                            <Typography variant="caption" sx={{ color: "#64748b" }}>
                              ({status.percentage.toFixed(1)}%)
                            </Typography>
                          </Box>
                        </Box>
                      ))}
                    </Box>
                  </Paper>
                </Fade>
              </Grid>
            </Grid>
          </Box>
        </Fade>
      </Container>
    </Box>
  );
};

export default AdminAnalytics;