import React, { useState, useEffect } from "react";
import {
  Box,
  Typography,
  Grid,
  Paper,
  Card,
  CardContent,
  CircularProgress,
  Alert,
  TextField,
  MenuItem,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Divider,
} from "@mui/material";
import {
  TrendingUp,
  TrendingDown,
  Assessment,
  Timeline,
  AttachMoney,
  ShoppingCart,
  People,
  LocalShipping,
} from "@mui/icons-material";
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";
import { revenueAPI } from "../../../services/api";
import { DailyRevenue, MonthlyRevenue, RevenueStats, TopProduct, OrderStatus } from "../../../types";
import { toast } from "react-toastify";

const AdminAnalytics: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [dateRange, setDateRange] = useState("30days");
  const [chartType, setChartType] = useState("daily");

  // State for data
  const [revenueStats, setRevenueStats] = useState<RevenueStats | null>(null);
  const [dailyRevenue, setDailyRevenue] = useState<DailyRevenue[]>([]);
  const [monthlyRevenue, setMonthlyRevenue] = useState<MonthlyRevenue[]>([]);
  const [topProducts, setTopProducts] = useState<TopProduct[]>([]);
  const [orderStatusStats, setOrderStatusStats] = useState<OrderStatus[]>([]);

  // Fetch data
  const fetchData = async () => {
    try {
      setLoading(true);
      setError("");

      const endDate = new Date();
      const startDate = new Date();

      // Calculate date range
      switch (dateRange) {
        case "7days":
          startDate.setDate(endDate.getDate() - 7);
          break;
        case "30days":
          startDate.setDate(endDate.getDate() - 30);
          break;
        case "90days":
          startDate.setDate(endDate.getDate() - 90);
          break;
        default:
          startDate.setDate(endDate.getDate() - 30);
      }

      const [statsRes, dailyRes, monthlyRes, topProductsRes, statusRes] = await Promise.all([
        revenueAPI.getRevenueStats(),
        revenueAPI.getDailyRevenue(startDate.toISOString().split('T')[0], endDate.toISOString().split('T')[0]),
        revenueAPI.getMonthlyRevenue(endDate.getFullYear()),
        revenueAPI.getTopProducts(10, startDate.toISOString().split('T')[0], endDate.toISOString().split('T')[0]),
        revenueAPI.getOrderStatusStats(startDate.toISOString().split('T')[0], endDate.toISOString().split('T')[0])
      ]);

      setRevenueStats(statsRes);
      setDailyRevenue(dailyRes);
      setMonthlyRevenue(monthlyRes);
      setTopProducts(topProductsRes);
      setOrderStatusStats(statusRes);

    } catch (error: any) {
      console.error('Error fetching analytics data:', error);
      setError(error.message || "Có lỗi xảy ra khi tải dữ liệu");
      toast.error("Không thể tải dữ liệu thống kê");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [dateRange]);

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN');
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress size={60} sx={{ color: "#9e655c" }} />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box sx={{
      p: { xs: 2, md: 3 },
      bgcolor: "#f8fafc",
      minHeight: "100vh"
    }}>
      {/* Header */}
      <Box
        display="flex"
        flexDirection={{ xs: "column", md: "row" }}
        justifyContent="space-between"
        alignItems={{ xs: "flex-start", md: "center" }}
        mb={4}
        gap={2}
      >
        <Typography
          variant="h4"
          sx={{
            fontWeight: 700,
            color: "#9e655c",
            display: "flex",
            alignItems: "center",
            gap: 2,
            fontSize: { xs: "1.75rem", md: "2rem" }
          }}
        >
          <Assessment sx={{ fontSize: { xs: 28, md: 30 } }} />
          Thống kê Doanh thu
        </Typography>

        <Box display="flex" gap={2} flexWrap="wrap">
          <TextField
            select
            size="small"
            value={dateRange}
            onChange={(e) => setDateRange(e.target.value)}
            sx={{
              minWidth: 120,
              "& .MuiOutlinedInput-root": {
                borderRadius: 2,
                bgcolor: "white"
              }
            }}
          >
            <MenuItem value="7days">7 ngày</MenuItem>
            <MenuItem value="30days">30 ngày</MenuItem>
            <MenuItem value="90days">90 ngày</MenuItem>
          </TextField>

          <TextField
            select
            size="small"
            value={chartType}
            onChange={(e) => setChartType(e.target.value)}
            sx={{
              minWidth: 120,
              "& .MuiOutlinedInput-root": {
                borderRadius: 2,
                bgcolor: "white"
              }
            }}
          >
            <MenuItem value="daily">Theo ngày</MenuItem>
            <MenuItem value="monthly">Theo tháng</MenuItem>
          </TextField>
        </Box>
      </Box>

      {/* Stats Cards */}
      {revenueStats && (
        <Grid container spacing={{ xs: 2, md: 3 }} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card
              sx={{
                borderRadius: 4,
                background: "linear-gradient(135deg, #4caf50 0%, #66bb6a 100%)",
                color: "white",
                transition: "all 0.3s ease",
                "&:hover": {
                  transform: "translateY(-8px)",
                  boxShadow: "0 20px 40px rgba(76, 175, 80, 0.3)"
                },
                height: "100%",
                boxShadow: "0 8px 32px rgba(76, 175, 80, 0.2)"
              }}
            >
              <CardContent sx={{ p: { xs: 2.5, md: 3.5 } }}>
                <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                  <Box
                    sx={{
                      p: 1.5,
                      borderRadius: 2,
                      bgcolor: "rgba(255,255,255,0.2)",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center"
                    }}
                  >
                    <AttachMoney sx={{ fontSize: { xs: 24, md: 28 } }} />
                  </Box>
                  <Typography variant="h5" sx={{ fontWeight: 700, fontSize: { xs: "1.25rem", md: "1.5rem" } }}>
                    {formatCurrency(revenueStats.thisMonthRevenue)}
                  </Typography>
                </Box>
                <Typography variant="body2" sx={{ opacity: 0.9, mb: 1.5, fontSize: "0.875rem" }}>
                  Doanh thu tháng này
                </Typography>
                <Box display="flex" alignItems="center">
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      bgcolor: "rgba(255,255,255,0.2)",
                      borderRadius: 1,
                      px: 1,
                      py: 0.5
                    }}
                  >
                    {revenueStats.monthlyGrowthPercentage >= 0 ? (
                      <TrendingUp sx={{ fontSize: 16, mr: 0.5 }} />
                    ) : (
                      <TrendingDown sx={{ fontSize: 16, mr: 0.5 }} />
                    )}
                    <Typography variant="body2" sx={{ fontWeight: 600 }}>
                      {revenueStats.monthlyGrowthPercentage.toFixed(1)}%
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card
              sx={{
                borderRadius: 4,
                background: "linear-gradient(135deg, #2196f3 0%, #42a5f5 100%)",
                color: "white",
                transition: "all 0.3s ease",
                "&:hover": {
                  transform: "translateY(-8px)",
                  boxShadow: "0 20px 40px rgba(33, 150, 243, 0.3)"
                },
                height: "100%",
                boxShadow: "0 8px 32px rgba(33, 150, 243, 0.2)"
              }}
            >
              <CardContent sx={{ p: { xs: 2.5, md: 3.5 } }}>
                <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                  <Box
                    sx={{
                      p: 1.5,
                      borderRadius: 2,
                      bgcolor: "rgba(255,255,255,0.2)",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center"
                    }}
                  >
                    <ShoppingCart sx={{ fontSize: { xs: 24, md: 28 } }} />
                  </Box>
                  <Typography variant="h5" sx={{ fontWeight: 700, fontSize: { xs: "1.25rem", md: "1.5rem" } }}>
                    {revenueStats.thisMonthOrders}
                  </Typography>
                </Box>
                <Typography variant="body2" sx={{ opacity: 0.9, mb: 1.5, fontSize: "0.875rem" }}>
                  Đơn hàng tháng này
                </Typography>
                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    bgcolor: "rgba(255,255,255,0.2)",
                    borderRadius: 1,
                    px: 1,
                    py: 0.5
                  }}
                >
                  <Typography variant="body2" sx={{ fontWeight: 600 }}>
                    Hoàn thành: {revenueStats.completedOrders}
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card
              sx={{
                borderRadius: 4,
                background: "linear-gradient(135deg, #ff9800 0%, #ffb74d 100%)",
                color: "white",
                transition: "all 0.3s ease",
                "&:hover": {
                  transform: "translateY(-8px)",
                  boxShadow: "0 20px 40px rgba(255, 152, 0, 0.3)"
                },
                height: "100%",
                boxShadow: "0 8px 32px rgba(255, 152, 0, 0.2)"
              }}
            >
              <CardContent sx={{ p: { xs: 2.5, md: 3.5 } }}>
                <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                  <Box
                    sx={{
                      p: 1.5,
                      borderRadius: 2,
                      bgcolor: "rgba(255,255,255,0.2)",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center"
                    }}
                  >
                    <Timeline sx={{ fontSize: { xs: 24, md: 28 } }} />
                  </Box>
                  <Typography variant="h5" sx={{ fontWeight: 700, fontSize: { xs: "1.25rem", md: "1.5rem" } }}>
                    {formatCurrency(revenueStats.averageOrderValue)}
                  </Typography>
                </Box>
                <Typography variant="body2" sx={{ opacity: 0.9, mb: 1.5, fontSize: "0.875rem" }}>
                  Giá trị đơn hàng TB
                </Typography>
                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    bgcolor: "rgba(255,255,255,0.2)",
                    borderRadius: 1,
                    px: 1,
                    py: 0.5
                  }}
                >
                  <Typography variant="body2" sx={{ fontWeight: 600 }}>
                    Hôm nay: {formatCurrency(revenueStats.todayRevenue)}
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card
              sx={{
                borderRadius: 4,
                background: "linear-gradient(135deg, #9c27b0 0%, #ba68c8 100%)",
                color: "white",
                transition: "all 0.3s ease",
                "&:hover": {
                  transform: "translateY(-8px)",
                  boxShadow: "0 20px 40px rgba(156, 39, 176, 0.3)"
                },
                height: "100%",
                boxShadow: "0 8px 32px rgba(156, 39, 176, 0.2)"
              }}
            >
              <CardContent sx={{ p: { xs: 2.5, md: 3.5 } }}>
                <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                  <Box
                    sx={{
                      p: 1.5,
                      borderRadius: 2,
                      bgcolor: "rgba(255,255,255,0.2)",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center"
                    }}
                  >
                    <LocalShipping sx={{ fontSize: { xs: 24, md: 28 } }} />
                  </Box>
                  <Typography variant="h5" sx={{ fontWeight: 700, fontSize: { xs: "1.25rem", md: "1.5rem" } }}>
                    {revenueStats.pendingOrders}
                  </Typography>
                </Box>
                <Typography variant="body2" sx={{ opacity: 0.9, mb: 1.5, fontSize: "0.875rem" }}>
                  Đơn hàng chờ xử lý
                </Typography>
                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    bgcolor: "rgba(255,255,255,0.2)",
                    borderRadius: 1,
                    px: 1,
                    py: 0.5
                  }}
                >
                  <Typography variant="body2" sx={{ fontWeight: 600 }}>
                    Đã hủy: {revenueStats.cancelledOrders}
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Revenue Chart - Full Width Row */}
      <Grid container spacing={{ xs: 2, md: 3 }} sx={{ mb: 4 }}>
        <Grid item xs={12}>
          <Paper
            sx={{
              p: { xs: 2.5, md: 3 },
              borderRadius: 4,
              border: "1px solid rgba(158, 101, 92, 0.1)",
              background: "linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)",
              boxShadow: "0 8px 32px rgba(158, 101, 92, 0.1)",
              transition: "all 0.3s ease",
              "&:hover": {
                boxShadow: "0 12px 48px rgba(158, 101, 92, 0.15)"
              }
            }}
          >
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={4}>
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 700,
                  color: "#9e655c",
                  display: "flex",
                  alignItems: "center",
                  gap: 1.5,
                  fontSize: { xs: "1.125rem", md: "1.25rem" }
                }}
              >
                📈 Biểu đồ doanh thu
              </Typography>
            </Box>

            <Box sx={{ height: { xs: 280, md: 380 } }}>
              <ResponsiveContainer width="100%" height="100%">
                {chartType === "daily" ? (
                  <AreaChart data={dailyRevenue}>
                    <defs>
                      <linearGradient id="colorRevenue" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#9e655c" stopOpacity={0.8}/>
                        <stop offset="95%" stopColor="#9e655c" stopOpacity={0.1}/>
                      </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
                    <XAxis
                      dataKey="date"
                      tickFormatter={(value) => formatDate(value)}
                      stroke="#666"
                    />
                    <YAxis
                      tickFormatter={(value) => `${(value / 1000000).toFixed(1)}M`}
                      stroke="#666"
                    />
                    <Tooltip
                      formatter={(value: any) => [formatCurrency(value), "Doanh thu"]}
                      labelFormatter={(label) => `Ngày: ${formatDate(label)}`}
                      contentStyle={{
                        backgroundColor: "#fff",
                        border: "1px solid #9e655c",
                        borderRadius: "8px",
                      }}
                    />
                    <Area
                      type="monotone"
                      dataKey="revenue"
                      stroke="#9e655c"
                      strokeWidth={3}
                      fillOpacity={1}
                      fill="url(#colorRevenue)"
                    />
                  </AreaChart>
                ) : (
                  <BarChart data={monthlyRevenue}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
                    <XAxis
                      dataKey="monthName"
                      stroke="#666"
                    />
                    <YAxis
                      tickFormatter={(value) => `${(value / 1000000).toFixed(1)}M`}
                      stroke="#666"
                    />
                    <Tooltip
                      formatter={(value: any) => [formatCurrency(value), "Doanh thu"]}
                      contentStyle={{
                        backgroundColor: "#fff",
                        border: "1px solid #9e655c",
                        borderRadius: "8px",
                      }}
                    />
                    <Bar
                      dataKey="revenue"
                      fill="#9e655c"
                      radius={[4, 4, 0, 0]}
                    />
                  </BarChart>
                )}
              </ResponsiveContainer>
            </Box>
          </Paper>
        </Grid>
      </Grid>

      {/* Second Row - Top Products and Order Status */}
      <Grid container spacing={{ xs: 2, md: 3 }}>
        <Grid item xs={12} md={6}>
          <Paper
            sx={{
              p: { xs: 2.5, md: 3 },
              borderRadius: 4,
              border: "1px solid rgba(158, 101, 92, 0.1)",
              background: "linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)",
              boxShadow: "0 8px 32px rgba(158, 101, 92, 0.1)",
              transition: "all 0.3s ease",
              "&:hover": {
                boxShadow: "0 12px 48px rgba(158, 101, 92, 0.15)"
              },
              height: "100%"
            }}
          >
            <Typography
              variant="h6"
              sx={{
                mb: 3,
                fontWeight: 700,
                color: "#9e655c",
                display: "flex",
                alignItems: "center",
                gap: 1.5,
                fontSize: { xs: "1.125rem", md: "1.25rem" }
              }}
            >
              🏆 Top sản phẩm
            </Typography>

            <List dense>
              {topProducts.slice(0, 5).map((product, index) => (
                <ListItem key={product.productId} sx={{ px: 0, py: 1 }}>
                  <ListItemAvatar>
                    <Avatar
                      src={product.imageUrl}
                      alt={product.productName}
                      sx={{
                        width: 40,
                        height: 40,
                        border: "2px solid #9e655c",
                      }}
                    />
                  </ListItemAvatar>
                  <ListItemText
                    primary={
                      <Typography variant="body2" sx={{ fontWeight: 600 }}>
                        {product.productName}
                      </Typography>
                    }
                    secondary={
                      <Box>
                        <Typography variant="caption" color="text.secondary">
                          Đã bán: {product.totalSold}
                        </Typography>
                        <Typography variant="caption" sx={{ display: "block", color: "#4caf50", fontWeight: 600 }}>
                          {formatCurrency(product.totalRevenue)}
                        </Typography>
                      </Box>
                    }
                  />
                  <Chip
                    label={`#${index + 1}`}
                    size="small"
                    sx={{
                      bgcolor: index === 0 ? "#ffd700" : index === 1 ? "#c0c0c0" : index === 2 ? "#cd7f32" : "#e0e0e0",
                      color: index < 3 ? "#fff" : "#666",
                      fontWeight: 700,
                    }}
                  />
                </ListItem>
              ))}
            </List>
          </Paper>
        </Grid>

        <Grid item xs={12} md={6}>
          <Paper
            sx={{
              p: { xs: 2.5, md: 3 },
              borderRadius: 4,
              border: "1px solid rgba(158, 101, 92, 0.1)",
              background: "linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)",
              boxShadow: "0 8px 32px rgba(158, 101, 92, 0.1)",
              transition: "all 0.3s ease",
              "&:hover": {
                boxShadow: "0 12px 48px rgba(158, 101, 92, 0.15)"
              },
              height: "100%"
            }}
          >
            <Typography
              variant="h6"
              sx={{
                mb: 3,
                fontWeight: 700,
                color: "#9e655c",
                display: "flex",
                alignItems: "center",
                gap: 1.5,
                fontSize: { xs: "1.125rem", md: "1.25rem" }
              }}
            >
              📊 Trạng thái đơn hàng
            </Typography>

            <Box sx={{ height: { xs: 200, md: 250 }, mb: 2 }}>
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={orderStatusStats}
                    cx="50%"
                    cy="50%"
                    innerRadius={50}
                    outerRadius={100}
                    paddingAngle={5}
                    dataKey="count"
                  >
                    {orderStatusStats.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip
                    formatter={(value: any, name) => [`${value} đơn`, "Số lượng"]}
                    contentStyle={{
                      backgroundColor: "#fff",
                      border: "1px solid #9e655c",
                      borderRadius: "8px",
                    }}
                  />
                </PieChart>
              </ResponsiveContainer>
            </Box>

            <Box sx={{ display: "flex", flexDirection: "column", gap: 1.5 }}>
              {orderStatusStats.map((status) => (
                <Box key={status.status} display="flex" alignItems="center" justifyContent="space-between">
                  <Box display="flex" alignItems="center" gap={1.5}>
                    <Box
                      sx={{
                        width: 14,
                        height: 14,
                        borderRadius: "50%",
                        bgcolor: status.color,
                      }}
                    />
                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                      {status.status}
                    </Typography>
                  </Box>
                  <Typography variant="body2" sx={{ fontWeight: 600, color: "#666" }}>
                    {status.count} ({status.percentage.toFixed(1)}%)
                  </Typography>
                </Box>
              ))}
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default AdminAnalytics;
