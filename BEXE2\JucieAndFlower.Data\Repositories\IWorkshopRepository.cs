﻿using JucieAndFlower.Data.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JucieAndFlower.Data.Repositories
{
    public interface IWorkshopRepository
    {
        Task<IEnumerable<Workshop>> GetAllAsync();
        Task<Workshop?> GetByIdAsync(int id);
        Task<Workshop> AddAsync(Workshop workshop);
        Task UpdateAsync(Workshop workshop);
        Task DeleteAsync(int id);
    }

}
