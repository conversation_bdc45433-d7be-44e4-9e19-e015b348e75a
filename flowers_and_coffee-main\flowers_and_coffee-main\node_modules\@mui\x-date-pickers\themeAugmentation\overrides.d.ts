import { DateCalendar<PERSON><PERSON><PERSON><PERSON>, DayCalendar<PERSON><PERSON><PERSON>ey, PickersFadeTransitionGroupClass<PERSON>ey, PickersSlideTransitionClassKey } from "../DateCalendar/index.js";
import { PickersCalendarHeaderClassKey } from "../PickersCalendarHeader/index.js";
import { DayCalendarSkeletonClassKey } from "../DayCalendarSkeleton/index.js";
import { Clock<PERSON>lass<PERSON>ey, ClockNumberClassKey, TimeClockClassKey, ClockPointerClassKey } from "../TimeClock/index.js";
import { MonthCalendarClassKey } from "../MonthCalendar/index.js";
import { PickersDayClassKey } from "../PickersDay/index.js";
import { YearCalendarClassKey } from "../YearCalendar/index.js";
import { PickersLayoutClassKey } from "../PickersLayout/index.js";
import { DatePickerToolbarClassKey } from "../DatePicker/index.js";
import { TimePickerToolbar<PERSON>lassKey } from "../TimePicker/index.js";
import { DateTimePickerToolbarClassKey, DateTimePickerTabsClassKey } from "../DateTimePicker/index.js";
import { PickersArrowSwitcherClassKey } from "../internals/components/PickersArrowSwitcher/index.js";
import { PickersToolbarClassKey } from "../internals/components/pickersToolbarClasses.js";
import { PickerPopperClassKey } from "../internals/components/PickerPopper/index.js";
import { PickersToolbarButtonClassKey } from "../internals/components/pickersToolbarButtonClasses.js";
import { PickersToolbarTextClassKey } from "../internals/components/pickersToolbarTextClasses.js";
import { DigitalClockClassKey } from "../DigitalClock/index.js";
import { MultiSectionDigitalClockClassKey, MultiSectionDigitalClockSectionClassKey } from "../MultiSectionDigitalClock/index.js";
import { PickersTextFieldClassKey, PickersInputClassKey, PickersOutlinedInputClassKey, PickersFilledInputClassKey, PickersInputBaseClassKey } from "../PickersTextField/index.js";
import { PickersSectionListClassKey } from "../PickersSectionList/index.js";
export interface PickersComponentNameToClassKey {
  MuiClock: ClockClassKey;
  MuiClockNumber: ClockNumberClassKey;
  MuiClockPointer: ClockPointerClassKey;
  MuiDateCalendar: DateCalendarClassKey;
  MuiDatePickerToolbar: DatePickerToolbarClassKey;
  MuiDateTimePickerTabs: DateTimePickerTabsClassKey;
  MuiDateTimePickerToolbar: DateTimePickerToolbarClassKey;
  MuiDayCalendar: DayCalendarClassKey;
  MuiDayCalendarSkeleton: DayCalendarSkeletonClassKey;
  MuiDigitalClock: DigitalClockClassKey;
  MuiMonthCalendar: MonthCalendarClassKey;
  MuiMultiSectionDigitalClock: MultiSectionDigitalClockClassKey;
  MuiMultiSectionDigitalClockSection: MultiSectionDigitalClockSectionClassKey;
  MuiPickersArrowSwitcher: PickersArrowSwitcherClassKey;
  MuiPickersCalendarHeader: PickersCalendarHeaderClassKey;
  MuiPickersDay: PickersDayClassKey;
  MuiPickersFadeTransitionGroup: PickersFadeTransitionGroupClassKey;
  MuiPickersLayout: PickersLayoutClassKey;
  MuiPickerPopper: PickerPopperClassKey;
  MuiPickersSlideTransition: PickersSlideTransitionClassKey;
  MuiPickersToolbar: PickersToolbarClassKey;
  MuiPickersToolbarButton: PickersToolbarButtonClassKey;
  MuiPickersToolbarText: PickersToolbarTextClassKey;
  MuiTimeClock: TimeClockClassKey;
  MuiTimePickerToolbar: TimePickerToolbarClassKey;
  MuiYearCalendar: YearCalendarClassKey;
  MuiPickersTextField: PickersTextFieldClassKey;
  MuiPickersInputBase: PickersInputBaseClassKey;
  MuiPickersInput: PickersInputClassKey;
  MuiPickersFilledInput: PickersFilledInputClassKey;
  MuiPickersOutlinedInput: PickersOutlinedInputClassKey;
  MuiPickersSectionList: PickersSectionListClassKey;
}
declare module '@mui/material/styles' {
  interface ComponentNameToClassKey extends PickersComponentNameToClassKey {}
}
export {};