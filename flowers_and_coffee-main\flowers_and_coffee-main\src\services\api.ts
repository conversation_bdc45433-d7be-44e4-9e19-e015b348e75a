import { LoginResponse } from '../types';
import { Product, CartItem } from '../types';

// API Base URL - Use relative path for proxy
const API_BASE_URL = '/api';

// Helper function to get auth token
const getAuthToken = () => {
  const token = localStorage.getItem('token') || '';
  console.log('Getting token from localStorage:', token ? `${token.substring(0, 20)}...` : 'No token found');

  // Debug: Parse and log token payload
  if (token) {
    try {
      const tokenPayload = JSON.parse(atob(token.split('.')[1]));
      console.log('Token payload:', tokenPayload);
      console.log('User ID claim:', tokenPayload['http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier']);
      console.log('Token expiry:', new Date(tokenPayload.exp * 1000));
    } catch (e) {
      console.error('Error parsing token:', e);
    }
  }

  return token;
};

// Helper function to make authenticated requests
const makeAuthenticatedRequest = async (url: string, options: RequestInit = {}) => {
  const token = getAuthToken();
  console.log('🔐 makeAuthenticatedRequest called for:', url);
  console.log('🔐 Token being sent:', token ? `${token.substring(0, 20)}...` : 'No token');

  const headers = {
    'Content-Type': 'application/json',
    ...(token && { 'Authorization': `Bearer ${token}` }),
    ...options.headers,
  };

  console.log('🔐 Request headers:', headers);
  console.log('🔐 Full request URL:', `${API_BASE_URL}${url}`);
  console.log('🔐 Request options:', { ...options, headers });
  console.log('🔐 Fetch options for makeAuthenticatedRequest:', {
    url: `${API_BASE_URL}${url}`,
    method: options.method || 'GET',
    headers: headers,
    body: options.body ? JSON.parse(options.body as string) : undefined // Log body if present
  });

  const response = await fetch(`${API_BASE_URL}${url}`, {
    ...options,
    headers,
  });

  console.log('🔐 Response status:', response.status);
  console.log('🔐 Response headers:', Object.fromEntries(response.headers.entries()));

  if (!response.ok) {
    let errorText = '';
    try {
      // Clone response to avoid "body stream already read" error
      const responseClone = response.clone();
      errorText = await responseClone.text();
      console.log('🔐 Error response text:', errorText);
    } catch (readError) {
      console.log('🔐 Could not read error response:', readError);
      errorText = `HTTP ${response.status} ${response.statusText}`;
    }
    console.error(`🔐 HTTP ${response.status} error for ${url}:`, errorText);
    throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
  }

  try {
    const jsonData = await response.json();
    console.log('🔐 Response JSON data:', jsonData);
    return jsonData;
  } catch (parseError) {
    console.error('🔐 Error parsing JSON response:', parseError);
    throw new Error('Invalid JSON response from server');
  }
};

// Auth API
export const authAPI = {
  login: async (email: string, password: string): Promise<LoginResponse> => {
    console.log('Login attempt:', { email, password: '***' });

    const response = await fetch(`${API_BASE_URL}/Auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, password }),
    });

    console.log('Login response status:', response.status);

    if (!response.ok) {
      // Try to get error message from response
      try {
        const errorData = await response.json();
        console.log('Login error data:', errorData);
        throw new Error(errorData.message || errorData.error || 'Login failed');
      } catch (error: any) {
        const errorText = await response.text();
        console.log('Login error text:', errorText);
        throw new Error('Login failed');
      }
    }

    const result: LoginResponse = await response.json();
    console.log('Login success result:', result);
    return result;
  },

  register: async (userData: {
    fullName: string;
    email: string;
    password: string;
    phone: string;
    address: string;
    gender: string;
    birthDate: string;
  }) => {
    const response = await fetch(`${API_BASE_URL}/Auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(userData),
    });

    if (!response.ok) {
      // Try to get error message from response
      try {
        const errorData = await response.json();
        throw new Error(errorData.error || errorData.message || 'Registration failed');
      } catch (error: any) {
        throw new Error('Registration failed');
      }
    }

    return response.json();
  },

  getProfile: async () => {
    const profileData = await makeAuthenticatedRequest('/Auth/profile');
    console.log('Raw profile data from backend:', profileData);
    console.log('Available properties:', Object.keys(profileData));

    // Handle case sensitivity for roleId
    const roleId = profileData.roleId || profileData.RoleId || profileData.roleid || profileData.ROLEID;

    console.log('Extracted roleId:', roleId);
    console.log('roleId type:', typeof roleId);

    // Transform backend UserProfileDto to frontend User interface
    const user = {
      userId: 0, // Backend doesn't return userId, set to 0 for now
      fullName: profileData.fullName || profileData.FullName,
      email: profileData.email || profileData.Email,
      phone: profileData.phone || profileData.Phone,
      address: profileData.address || profileData.Address,
      gender: profileData.gender || profileData.Gender,
      birthDate: profileData.birthDate || profileData.BirthDate,
      roleId: roleId,
      createdAt: new Date().toISOString(), // Backend doesn't return this
    };

    console.log('Transformed user data:', user);
    console.log('User roleId after transform:', user.roleId);
    console.log('User roleId type after transform:', typeof user.roleId);

    // Validate that we have roleId
    if (user.roleId === undefined || user.roleId === null) {
      console.error('❌ CRITICAL: roleId is missing from profile data!');
      console.error('Raw profile data:', profileData);
      throw new Error('Profile data is missing roleId');
    }

    return user;
  },

  updateProfile: async (userData: {
    fullName?: string;
    phone?: string;
    address?: string;
    gender?: string;
    birthDate?: string;
    oldPassword?: string;
    newPassword?: string;
    confirmPassword?: string;
  }) => {
    const token = getAuthToken();
    console.log('Update profile data:', userData);

    const headers = {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` }),
    };

    const response = await fetch(`${API_BASE_URL}/Auth/update-profile`, {
      method: 'PUT',
      headers,
      body: JSON.stringify(userData),
    });

    console.log('Update profile response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.log('Update profile error:', errorText);

      try {
        const errorData = JSON.parse(errorText);
        throw new Error(errorData.message || errorData.error || 'Update failed');
      } catch (error: any) {
        throw new Error(errorText || 'Update failed');
      }
    }

    // Backend returns success message, not user data
    const result = await response.text();
    console.log('Update profile success:', result);
    return result;
  },

  logout: async () => {
    return makeAuthenticatedRequest('/Auth/logout', {
      method: 'POST',
    });
  },
};

// Products API
export const productsAPI = {
  getAll: async () => {
    console.log('🔄 Fetching products from:', `${API_BASE_URL}/Products`);

    const response = await fetch(`${API_BASE_URL}/Products`);
    console.log('📡 Products response status:', response.status);
    console.log('📡 Products response headers:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorText = await response.text();
      console.log('❌ Products error response:', errorText);
      throw new Error(`Failed to fetch products: ${response.status}`);
    }

    const result = await response.json();
    console.log('📦 RAW Products response data:', result);
    console.log('📦 Response type:', typeof result);
    console.log('📦 Is array:', Array.isArray(result));
    console.log('📦 Response keys:', Object.keys(result || {}));

    // Log first item structure if array
    if (Array.isArray(result) && result.length > 0) {
      console.log('📦 First product structure:', result[0]);
      console.log('📦 First product keys:', Object.keys(result[0] || {}));
    }

    // Transform PascalCase to camelCase for TypeScript
    const transformProduct = (product: any): Product => ({
      productId: product.ProductId,
      name: product.Name,
      description: product.Description,
      price: product.Price,
      imageUrl: product.ImageUrl,
      categoryId: product.CategoryId,
      isAvailable: product.IsAvailable,
      createdAt: product.CreatedAt
    });

    // Handle different response structures
    let products = [];
    if (Array.isArray(result)) {
      console.log('✅ Using direct array result');
      products = result;
    } else if (result.$values && Array.isArray(result.$values)) {
      console.log('✅ Using $values array result');
      products = result.$values;
    } else if (result.data && Array.isArray(result.data)) {
      console.log('✅ Using data array result');
      products = result.data;
    } else {
      console.warn('⚠️ Unexpected products response structure:', result);
      return [];
    }

    // Transform all products to camelCase
    const transformedProducts = products.map(transformProduct);
    console.log('🔄 Transformed products:', transformedProducts);
    console.log('🔄 First transformed product:', transformedProducts[0]);

    return transformedProducts;
  },

  getById: async (id: number) => {
    console.log('Fetching product by ID:', id);

    const response = await fetch(`${API_BASE_URL}/Products/${id}`);
    console.log('Product by ID response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.log('Product by ID error response:', errorText);
      throw new Error(`Failed to fetch product: ${response.status}`);
    }

    const result = await response.json();
    console.log('Product by ID response data:', result);

    // Transform PascalCase to camelCase
    const transformedProduct = {
      productId: result.ProductId,
      name: result.Name,
      description: result.Description,
      price: result.Price,
      imageUrl: result.ImageUrl,
      categoryId: result.CategoryId,
      isAvailable: result.IsAvailable,
      createdAt: result.CreatedAt
    };

    console.log('Transformed product by ID:', transformedProduct);
    return transformedProduct;
  },

  create: async (productData: {
    name: string;
    description: string;
    price: number;
    imageUrl: string;
    categoryId: number;
    isAvailable: boolean;
  }) => {
    return makeAuthenticatedRequest('/Products', {
      method: 'POST',
      body: JSON.stringify(productData),
    });
  },

  update: async (id: number, productData: {
    name: string;
    description: string;
    price: number;
    imageUrl: string;
    categoryId: number;
    isAvailable: boolean;
  }) => {
    console.log('Updating product API call:', { id, productData });
    console.log('Full API URL will be:', `${API_BASE_URL}/Products/${id}`);

    try {
      const token = getAuthToken();
      const headers = {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` }),
      };

      const response = await fetch(`${API_BASE_URL}/Products/${id}`, {
        method: 'PUT',
        headers,
        body: JSON.stringify(productData),
      });

      console.log('Update product response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.log('Update product error response:', errorText);
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // For PUT requests that return 200 OK with no content, just return success
      console.log('Update product successful');
      return { success: true };
    } catch (error: any) {
      console.error('Update product error details:', {
        message: error.message,
        stack: error.stack,
        url: `${API_BASE_URL}/Products/${id}`,
        method: 'PUT',
        data: productData
      });
      throw error;
    }
  },

  delete: async (id: number) => {
    return makeAuthenticatedRequest(`/Products/${id}`, {
      method: 'DELETE',
    });
  },
};

// Cart API
export const cartAPI = {
  getCart: async () => {
    console.log('Fetching cart...');
    const result = await makeAuthenticatedRequest('/Cart');
    console.log('Cart response:', result);

    // Handle different response structures
    let cartItems = [];
    if (Array.isArray(result)) {
      cartItems = result;
    } else if (result.$values && Array.isArray(result.$values)) {
      cartItems = result.$values;
    } else if (result.data && Array.isArray(result.data)) {
      cartItems = result.data;
    } else {
      console.warn('Unexpected cart response structure:', result);
      return [];
    }

    // Fetch product details for each cart item
    const enrichedCartItemsPromises = cartItems.map(async (item: any): Promise<CartItem | null> => {
      console.log('Processing item for enrichment:', item);
      console.log('Item structure before product fetch:', item);

      // Handle case sensitivity for backend response
      const itemId = item.id || item.Id;
      const userId = item.userId || item.UserId || 0;
      const productId = item.productId || item.ProductId || 0;
      const quantity = item.quantity || item.Quantity || 0;
      const productName = item.productName || item.ProductName || 'Unknown Product';

      console.log('Extracted values:', { itemId, userId, productId, quantity, productName });

      // Always return a structure, even if enrichment fails
      const baseCartItem = {
        id: itemId,
        userId: userId,
        productId: productId,
        quantity: quantity,
        product: { // Provide a basic product structure by default
          productId: productId,
          name: productName,
          price: 0,
          imageUrl: '',
          description: '',
          categoryId: 0,
          isAvailable: true,
          createdAt: ''
        }
      };

      if (!productId) {
        console.warn('Skipping product details fetch for item with missing ProductId:', item);
        return baseCartItem; // Return basic item if productId is missing
      }

      try {
        console.log('Fetching product details for ProductId:', productId);
        const productResponse = await fetch(`${API_BASE_URL}/Products/${productId}`);

        if (productResponse.ok) {
          const productData = await productResponse.json();
          console.log('Product data for', productId, ':', productData);
          console.log('Product price from API:', productData.Price, productData.price);

          // Return enriched item if fetch is successful
          return {
            ...baseCartItem, // Start with base info
            product: { // Override product with fetched data
              productId: productData.ProductId || productData.productId,
              name: productData.Name || productData.name || productName,
              price: productData.Price || productData.price || 0,
              imageUrl: productData.ImageUrl || productData.imageUrl || '',
              description: productData.Description || productData.description || '',
              categoryId: productData.CategoryId || productData.categoryId || 0,
              isAvailable: productData.IsAvailable !== false && productData.isAvailable !== false,
              createdAt: productData.CreatedAt || productData.createdAt || ''
            }
          };
        } else {
          console.warn('Failed to fetch product details for', productId, ':', productResponse.status);
          // Return basic item if fetch fails (e.g., 404)
          return baseCartItem;
        }
      } catch (error: any) {
        console.error('Error fetching product details for', productId, ':', error);
        // Return basic item structure in case of exception during fetch
        return baseCartItem;
      }
    });

    const enrichedCartItems = await Promise.all(enrichedCartItemsPromises);
    // No need to filter null/undefined now, as we always return an object

    console.log('Enriched cart items:', enrichedCartItems);
    return enrichedCartItems;
  },

  addItem: async (productId: number, quantity: number) => {
    console.log('Adding item to cart:', { productId, quantity });
    const result = await makeAuthenticatedRequest('/Cart', {
      method: 'POST',
      body: JSON.stringify({ productId, quantity }),
    });
    console.log('Add to cart response:', result);
    return result;
  },

  updateItem: async (productId: number, quantity: number) => {
    console.log('Updating cart item:', { productId, quantity });

    // Backend AddOrUpdateCartItem adds quantity, so we need to:
    // 1. Remove the item first
    // 2. Add it with the new quantity
    try {
      // Remove first
      await makeAuthenticatedRequest(`/Cart/${productId}`, {
        method: 'DELETE',
      });

      // Then add with new quantity
      const result = await makeAuthenticatedRequest('/Cart', {
        method: 'POST',
        body: JSON.stringify({ productId, quantity }),
      });
      console.log('Update cart item response:', result);
      return result;
    } catch (error) {
      console.error('Error updating cart item:', error);
      throw error;
    }
  },

  removeItem: async (productId: number) => {
    console.log('Removing item from cart:', productId);
    const result = await makeAuthenticatedRequest(`/Cart/${productId}`, {
      method: 'DELETE',
    });
    console.log('Remove from cart response:', result);
    return result;
  },
};

// Orders API
export const ordersAPI = {
  createFromCart: async (orderData: {
    note: string;
    deliveryAddress: string;
    promotionCode?: string;
    selectedCartItemIds?: number[];
  }) => {
    console.log('Creating order from cart:', orderData);

    // If no selectedCartItemIds provided, get all cart items
    if (!orderData.selectedCartItemIds || orderData.selectedCartItemIds.length === 0) {
      console.log('No selectedCartItemIds provided, fetching raw cart items...');
      try {
        // Get raw cart data from backend (not enriched)
        const rawCartData = await makeAuthenticatedRequest('/Cart');
        console.log('Raw cart data for order:', rawCartData);

        // Handle different response structures
        let cartItems = [];
        if (Array.isArray(rawCartData)) {
          cartItems = rawCartData;
        } else if (rawCartData.$values && Array.isArray(rawCartData.$values)) {
          cartItems = rawCartData.$values;
        } else if (rawCartData.data && Array.isArray(rawCartData.data)) {
          cartItems = rawCartData.data;
        }

        // Extract cart item IDs from backend response and filter/parse to ensure valid integers
        orderData.selectedCartItemIds = cartItems
          .map((item: any): number | null => {
            // Backend returns 'Id' (PascalCase), frontend expects 'id' (camelCase)
            const idValue = item ? (item.id || item.Id) : undefined; // Try both cases
            const id = parseInt(idValue, 10);

            return !isNaN(id) ? id : null;
          })
          .filter((id: number | null): id is number => id !== null) as number[];
        console.log('Selected cart item IDs:', orderData.selectedCartItemIds);

        if (orderData.selectedCartItemIds.length === 0) {
          throw new Error('Giỏ hàng trống');
        }
      } catch (error) {
        console.error('Error fetching cart items for order:', error);
        throw new Error('Không thể lấy thông tin giỏ hàng để tạo đơn hàng');
      }
    }

    const result = await makeAuthenticatedRequest('/Orders/from-cart', {
      method: 'POST',
      body: JSON.stringify(orderData),
    });
    console.log('Create order response:', result);

    // Handle new response structure with Success field
    if (result && result.Success === false) {
      throw new Error(result.Message || 'Failed to create order');
    }

    // Return the result, handling both old and new response structures
    return {
      orderId: result.OrderId || result.orderId,
      paymentUrl: result.PaymentUrl || result.paymentUrl
    };
  },

  getOrders: async () => {
    console.log('Fetching orders...');
    const result = await makeAuthenticatedRequest('/Orders');
    console.log('Orders response:', result);

    // Handle different response structures
    if (Array.isArray(result)) {
      return result;
    } else if (result.$values && Array.isArray(result.$values)) {
      return result.$values;
    } else if (result.data && Array.isArray(result.data)) {
      return result.data;
    } else {
      console.warn('Unexpected orders response structure:', result);
      return [];
    }
  },

  getOrderById: async (id: number) => {
    console.log('Fetching order by ID:', id);
    const result = await makeAuthenticatedRequest(`/Orders/${id}`);
    console.log('Order by ID response:', result);
    return result;
  },
};

// Revenue API
export const revenueAPI = {
  getDailyRevenue: async (startDate?: string, endDate?: string) => {
    console.log('🔄 Fetching daily revenue...');
    const params = new URLSearchParams();
    if (startDate) params.append('startDate', startDate);
    if (endDate) params.append('endDate', endDate);

    const result = await makeAuthenticatedRequest(`/Revenue/daily?${params.toString()}`);
    console.log('📊 Daily revenue response:', result);

    // Transform PascalCase to camelCase
    const transformDailyRevenue = (item: any): any => ({
      date: item.Date,
      revenue: item.Revenue,
      orderCount: item.OrderCount,
      averageOrderValue: item.AverageOrderValue,
      completedOrders: item.CompletedOrders,
      pendingOrders: item.PendingOrders,
      cancelledOrders: item.CancelledOrders
    });

    if (Array.isArray(result)) {
      const transformed = result.map(transformDailyRevenue);
      console.log('🔄 Transformed daily revenue:', transformed);
      return transformed;
    }
    return result;
  },

  getMonthlyRevenue: async (year?: number) => {
    console.log('🔄 Fetching monthly revenue...');
    const params = year ? `?year=${year}` : '';
    const result = await makeAuthenticatedRequest(`/Revenue/monthly${params}`);
    console.log('📊 Monthly revenue response:', result);

    // Transform PascalCase to camelCase
    const transformMonthlyRevenue = (item: any): any => ({
      year: item.Year,
      month: item.Month,
      monthName: item.MonthName,
      revenue: item.Revenue,
      orderCount: item.OrderCount,
      averageOrderValue: item.AverageOrderValue,
      growthPercentage: item.GrowthPercentage
    });

    if (Array.isArray(result)) {
      const transformed = result.map(transformMonthlyRevenue);
      console.log('🔄 Transformed monthly revenue:', transformed);
      return transformed;
    }
    return result;
  },

  getRevenueStats: async () => {
    console.log('🔄 Fetching revenue stats...');
    const result = await makeAuthenticatedRequest('/Revenue/stats');
    console.log('📊 Revenue stats response:', result);

    // Transform PascalCase to camelCase
    const transformedStats = {
      totalRevenue: result.TotalRevenue,
      todayRevenue: result.TodayRevenue,
      thisWeekRevenue: result.ThisWeekRevenue,
      thisMonthRevenue: result.ThisMonthRevenue,
      lastMonthRevenue: result.LastMonthRevenue,
      monthlyGrowthPercentage: result.MonthlyGrowthPercentage,

      totalOrders: result.TotalOrders,
      todayOrders: result.TodayOrders,
      thisWeekOrders: result.ThisWeekOrders,
      thisMonthOrders: result.ThisMonthOrders,
      lastMonthOrders: result.LastMonthOrders,

      pendingOrders: result.PendingOrders,
      completedOrders: result.CompletedOrders,
      cancelledOrders: result.CancelledOrders,

      averageOrderValue: result.AverageOrderValue,
      highestDailyRevenue: result.HighestDailyRevenue,
      highestRevenueDate: result.HighestRevenueDate
    };

    console.log('🔄 Transformed revenue stats:', transformedStats);
    return transformedStats;
  },

  getTopProducts: async (limit = 10, startDate?: string, endDate?: string) => {
    console.log('🔄 Fetching top products...');
    const params = new URLSearchParams();
    params.append('limit', limit.toString());
    if (startDate) params.append('startDate', startDate);
    if (endDate) params.append('endDate', endDate);

    const result = await makeAuthenticatedRequest(`/Revenue/top-products?${params.toString()}`);
    console.log('📊 Top products response:', result);

    // Transform PascalCase to camelCase
    const transformTopProduct = (item: any): any => ({
      productId: item.ProductId,
      productName: item.ProductName,
      imageUrl: item.ImageUrl,
      totalSold: item.TotalSold,
      totalRevenue: item.TotalRevenue,
      price: item.Price,
      revenuePercentage: item.RevenuePercentage
    });

    if (Array.isArray(result)) {
      const transformed = result.map(transformTopProduct);
      console.log('🔄 Transformed top products:', transformed);
      return transformed;
    }
    return result;
  },

  getOrderStatusStats: async (startDate?: string, endDate?: string) => {
    console.log('🔄 Fetching order status stats...');
    const params = new URLSearchParams();
    if (startDate) params.append('startDate', startDate);
    if (endDate) params.append('endDate', endDate);

    const result = await makeAuthenticatedRequest(`/Revenue/order-status-stats?${params.toString()}`);
    console.log('📊 Order status stats response:', result);

    // Transform PascalCase to camelCase
    const transformOrderStatus = (item: any): any => ({
      status: item.Status,
      count: item.Count,
      totalAmount: item.TotalAmount,
      percentage: item.Percentage,
      color: item.Color
    });

    if (Array.isArray(result)) {
      const transformed = result.map(transformOrderStatus);
      console.log('🔄 Transformed order status stats:', transformed);
      return transformed;
    }
    return result;
  },
};

// Categories API
export const categoriesAPI = {
  getAll: async () => {
    console.log('🔄 Fetching categories from:', `${API_BASE_URL}/Category`);

    const response = await fetch(`${API_BASE_URL}/Category`);
    console.log('📡 Categories response status:', response.status);
    console.log('📡 Categories response headers:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorText = await response.text();
      console.log('❌ Categories error response:', errorText);
      throw new Error(`Failed to fetch categories: ${response.status}`);
    }

    const result = await response.json();
    console.log('📂 RAW Categories response data:', result);
    console.log('📂 Response type:', typeof result);
    console.log('📂 Is array:', Array.isArray(result));
    console.log('📂 Response keys:', Object.keys(result || {}));

    // Log first item structure if array
    if (Array.isArray(result) && result.length > 0) {
      console.log('📂 First category structure:', result[0]);
      console.log('📂 First category keys:', Object.keys(result[0] || {}));
    }

    // Transform PascalCase to camelCase for TypeScript
    const transformCategory = (category: any): any => ({
      categoryId: category.CategoryId,
      name: category.Name,
      description: category.Description
    });

    // Handle different response structures
    let categories = [];
    if (Array.isArray(result)) {
      console.log('✅ Using direct array result');
      categories = result;
    } else if (result.$values && Array.isArray(result.$values)) {
      console.log('✅ Using $values array result');
      categories = result.$values;
    } else if (result.data && Array.isArray(result.data)) {
      console.log('✅ Using data array result');
      categories = result.data;
    } else {
      console.warn('⚠️ Unexpected categories response structure:', result);
      return [];
    }

    // Transform all categories to camelCase
    const transformedCategories = categories.map(transformCategory);
    console.log('🔄 Transformed categories:', transformedCategories);
    console.log('🔄 First transformed category:', transformedCategories[0]);

    return transformedCategories;
  },
};

// Payments API
export const paymentsAPI = {
  getPayments: async () => {
    return makeAuthenticatedRequest('/Payment');
  },
};


