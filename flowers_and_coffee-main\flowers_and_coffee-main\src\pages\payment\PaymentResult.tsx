import React, { useEffect, useState } from "react";
import {
  Container,
  Paper,
  Typography,
  Box,
  Button,
  CircularProgress,
  Card,
  CardContent,
  Divider,
  Chip,
} from "@mui/material";
import {
  CheckCircle,
  Error,
  Home,
  Receipt,
  ShoppingBag,
} from "@mui/icons-material";
import { useNavigate, useSearchParams } from "react-router-dom";
import { ordersAPI } from "../../services/api";
import { toast } from "react-toastify";

const PaymentResult = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [loading, setLoading] = useState(true);
  const [paymentResult, setPaymentResult] = useState<any>(null);
  const [orderDetails, setOrderDetails] = useState<any>(null);

  useEffect(() => {
    const checkPaymentResult = async () => {
      try {
        // Lấy query parameters từ URL
        const success = searchParams.get('success') === 'true';
        const orderId = searchParams.get('orderId');
        const amount = searchParams.get('amount');
        const message = searchParams.get('message');

        if (!searchParams.has('success')) {
          toast.error("Không tìm thấy thông tin thanh toán");
          navigate("/");
          return;
        }

        // Tạo kết quả thanh toán từ query params
        const result = {
          success: success,
          orderId: orderId ? parseInt(orderId) : null,
          amount: amount ? parseFloat(amount) : null,
          message: message || (success ? "Thanh toán thành công" : "Thanh toán thất bại")
        };

        setPaymentResult(result);

        // Nếu có OrderId, lấy thông tin chi tiết đơn hàng
        if (result.orderId) {
          try {
            const orderData = await ordersAPI.getById(result.orderId);
            setOrderDetails(orderData);
          } catch (error) {
            console.error("Error fetching order details:", error);
          }
        }

        if (result.success) {
          toast.success("Thanh toán thành công!");
        } else {
          toast.error(result.message || "Thanh toán thất bại hoặc bị hủy");
        }
      } catch (error) {
        console.error("Error checking payment result:", error);
        toast.error("Có lỗi xảy ra khi kiểm tra kết quả thanh toán");
        navigate("/");
      } finally {
        setLoading(false);
      }
    };

    checkPaymentResult();
  }, [navigate, searchParams]);

  if (loading) {
    return (
      <Container maxWidth="md" sx={{ mt: 8, mb: 4, textAlign: "center" }}>
        <CircularProgress size={60} />
        <Typography variant="h6" sx={{ mt: 2 }}>
          Đang xử lý kết quả thanh toán...
        </Typography>
      </Container>
    );
  }

  const isSuccess = paymentResult?.success;

  return (
    <Container maxWidth="md" sx={{ mt: 4, mb: 4 }}>
      <Paper
        elevation={3}
        sx={{
          p: 4,
          textAlign: "center",
          background: isSuccess
            ? "linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%)"
            : "linear-gradient(135deg, #ffebee 0%, #fce4ec 100%)",
        }}
      >
        <Box sx={{ mb: 3 }}>
          {isSuccess ? (
            <CheckCircle
              sx={{
                fontSize: 80,
                color: "success.main",
                mb: 2,
              }}
            />
          ) : (
            <Error
              sx={{
                fontSize: 80,
                color: "error.main",
                mb: 2,
              }}
            />
          )}

          <Typography variant="h4" gutterBottom>
            {isSuccess ? "Thanh toán thành công!" : "Thanh toán thất bại"}
          </Typography>

          <Typography variant="body1" color="text.secondary">
            {isSuccess
              ? "Cảm ơn bạn đã mua hàng. Đơn hàng của bạn đã được xác nhận."
              : "Thanh toán không thành công. Vui lòng thử lại hoặc liên hệ hỗ trợ."}
          </Typography>
        </Box>

        {paymentResult?.orderId && (
          <Card sx={{ mb: 3, textAlign: "left" }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                <Receipt sx={{ mr: 1, verticalAlign: "middle" }} />
                Thông tin đơn hàng
              </Typography>
              <Divider sx={{ mb: 2 }} />

              <Box sx={{ display: "flex", justifyContent: "space-between", mb: 1 }}>
                <Typography variant="body2">Mã đơn hàng:</Typography>
                <Typography variant="body2" fontWeight="bold">
                  #{paymentResult.orderId}
                </Typography>
              </Box>

              {orderDetails && (
                <>
                  <Box sx={{ display: "flex", justifyContent: "space-between", mb: 1 }}>
                    <Typography variant="body2">Tổng tiền:</Typography>
                    <Typography variant="body2" fontWeight="bold">
                      {orderDetails.finalAmount?.toLocaleString("vi-VN")} VNĐ
                    </Typography>
                  </Box>

                  <Box sx={{ display: "flex", justifyContent: "space-between", mb: 1 }}>
                    <Typography variant="body2">Trạng thái:</Typography>
                    <Chip
                      label={isSuccess ? "Đã thanh toán" : "Đã hủy"}
                      color={isSuccess ? "success" : "error"}
                      size="small"
                    />
                  </Box>
                </>
              )}

              <Box sx={{ display: "flex", justifyContent: "space-between" }}>
                <Typography variant="body2">Phương thức thanh toán:</Typography>
                <Typography variant="body2" fontWeight="bold">
                  VNPay
                </Typography>
              </Box>
            </CardContent>
          </Card>
        )}

        <Box sx={{ display: "flex", gap: 2, justifyContent: "center", flexWrap: "wrap" }}>
          <Button
            variant="contained"
            startIcon={<Home />}
            onClick={() => navigate("/")}
            sx={{
              bgcolor: "#9e655c",
              "&:hover": { bgcolor: "#8a5a52" },
            }}
          >
            Về trang chủ
          </Button>

          {isSuccess && (
            <Button
              variant="outlined"
              startIcon={<Receipt />}
              onClick={() => navigate(`/orders/${paymentResult.orderId}`)}
              sx={{
                borderColor: "#9e655c",
                color: "#9e655c",
                "&:hover": {
                  borderColor: "#8a5a52",
                  bgcolor: "rgba(158, 101, 92, 0.04)",
                },
              }}
            >
              Xem đơn hàng
            </Button>
          )}

          <Button
            variant="outlined"
            startIcon={<ShoppingBag />}
            onClick={() => navigate("/flower")}
            sx={{
              borderColor: "#9e655c",
              color: "#9e655c",
              "&:hover": {
                borderColor: "#8a5a52",
                bgcolor: "rgba(158, 101, 92, 0.04)",
              },
            }}
          >
            Tiếp tục mua sắm
          </Button>
        </Box>
      </Paper>
    </Container>
  );
};

export default PaymentResult;
