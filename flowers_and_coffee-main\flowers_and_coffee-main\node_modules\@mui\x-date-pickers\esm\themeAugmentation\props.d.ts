import { DateCalendarProps, ExportedSlideTransitionProps, ExportedPickersFadeTransitionGroupProps } from "../DateCalendar/index.js";
import { DayCalendarSkeletonProps } from "../DayCalendarSkeleton/index.js";
import { ClockNumberProps, TimeClockProps, ClockPointerProps, ClockProps } from "../TimeClock/index.js";
import { MonthCalendarProps } from "../MonthCalendar/index.js";
import { PickersDayProps } from "../PickersDay/index.js";
import { YearCalendarProps } from "../YearCalendar/index.js";
import { DateFieldProps } from "../DateField/index.js";
import { LocalizationProviderProps } from "../LocalizationProvider/index.js";
import { PickersLayoutProps } from "../PickersLayout/index.js";
import { DayCalendarProps } from "../DateCalendar/DayCalendar.js";
import { ExportedPickersArrowSwitcherProps } from "../internals/components/PickersArrowSwitcher/PickersArrowSwitcher.types.js";
import { ExportedPickerPopperProps } from "../internals/components/PickerPopper/index.js";
import { PickersToolbarProps } from "../internals/components/PickersToolbar.js";
import { PickersToolbarButtonProps } from "../internals/components/PickersToolbarButton.js";
import { ExportedPickersToolbarTextProps } from "../internals/components/PickersToolbarText.js";
import { DatePickerProps } from "../DatePicker/index.js";
import { ExportedDatePickerToolbarProps } from "../DatePicker/DatePickerToolbar.js";
import { DesktopDatePickerProps } from "../DesktopDatePicker/index.js";
import { MobileDatePickerProps } from "../MobileDatePicker/index.js";
import { StaticDatePickerProps } from "../StaticDatePicker/index.js";
import { DateTimePickerProps, DateTimePickerTabsProps } from "../DateTimePicker/index.js";
import { ExportedDateTimePickerToolbarProps } from "../DateTimePicker/DateTimePickerToolbar.js";
import { DesktopDateTimePickerProps } from "../DesktopDateTimePicker/index.js";
import { MobileDateTimePickerProps } from "../MobileDateTimePicker/index.js";
import { StaticDateTimePickerProps } from "../StaticDateTimePicker/index.js";
import { DateTimeFieldProps } from "../DateTimeField/index.js";
import { TimePickerProps } from "../TimePicker/index.js";
import { ExportedTimePickerToolbarProps } from "../TimePicker/TimePickerToolbar.js";
import { DesktopTimePickerProps } from "../DesktopTimePicker/index.js";
import { MobileTimePickerProps } from "../MobileTimePicker/index.js";
import { StaticTimePickerProps } from "../StaticTimePicker/index.js";
import { ExportedDigitalClockProps } from "../DigitalClock/index.js";
import { TimeFieldProps } from "../TimeField/index.js";
import { ExportedMultiSectionDigitalClockSectionProps, MultiSectionDigitalClockProps } from "../MultiSectionDigitalClock/index.js";
import { ExportedPickersCalendarHeaderProps } from "../PickersCalendarHeader/index.js";
import { PickersTextFieldProps, PickersInputBaseProps, PickersOutlinedInputProps, PickersInputProps, PickersFilledInputProps } from "../PickersTextField/index.js";
import { PickersSectionListProps } from "../PickersSectionList/index.js";
import { PickerValidValue } from "../internals/models/index.js";
export interface PickersComponentsPropsList {
  MuiClock: ClockProps;
  MuiClockNumber: ClockNumberProps;
  MuiClockPointer: ClockPointerProps;
  MuiDateCalendar: DateCalendarProps;
  MuiDateField: DateFieldProps<any>;
  MuiDatePickerToolbar: ExportedDatePickerToolbarProps;
  MuiDateTimeField: DateTimeFieldProps<any>;
  MuiDateTimePickerTabs: DateTimePickerTabsProps;
  MuiDateTimePickerToolbar: ExportedDateTimePickerToolbarProps;
  MuiDayCalendar: DayCalendarProps;
  MuiDayCalendarSkeleton: DayCalendarSkeletonProps;
  MuiDigitalClock: ExportedDigitalClockProps;
  MuiLocalizationProvider: LocalizationProviderProps<unknown>;
  MuiMonthCalendar: MonthCalendarProps;
  MuiMultiSectionDigitalClock: MultiSectionDigitalClockProps;
  MuiMultiSectionDigitalClockSection: ExportedMultiSectionDigitalClockSectionProps;
  MuiPickersArrowSwitcher: ExportedPickersArrowSwitcherProps;
  MuiPickersCalendarHeader: ExportedPickersCalendarHeaderProps;
  MuiPickersDay: PickersDayProps;
  MuiPickersFadeTransitionGroup: ExportedPickersFadeTransitionGroupProps;
  MuiPickerPopper: ExportedPickerPopperProps;
  MuiPickersSlideTransition: ExportedSlideTransitionProps;
  MuiPickersToolbar: PickersToolbarProps;
  MuiPickersToolbarButton: PickersToolbarButtonProps;
  MuiPickersToolbarText: ExportedPickersToolbarTextProps;
  MuiPickersLayout: PickersLayoutProps<PickerValidValue>;
  MuiTimeClock: TimeClockProps;
  MuiTimeField: TimeFieldProps<any>;
  MuiTimePickerToolbar: ExportedTimePickerToolbarProps;
  MuiYearCalendar: YearCalendarProps;
  MuiDatePicker: DatePickerProps;
  MuiDesktopDatePicker: DesktopDatePickerProps;
  MuiMobileDatePicker: MobileDatePickerProps;
  MuiStaticDatePicker: StaticDatePickerProps;
  MuiTimePicker: TimePickerProps;
  MuiDesktopTimePicker: DesktopTimePickerProps;
  MuiMobileTimePicker: MobileTimePickerProps;
  MuiStaticTimePicker: StaticTimePickerProps;
  MuiDateTimePicker: DateTimePickerProps;
  MuiDesktopDateTimePicker: DesktopDateTimePickerProps;
  MuiMobileDateTimePicker: MobileDateTimePickerProps;
  MuiStaticDateTimePicker: StaticDateTimePickerProps;
  MuiPickersTextField: PickersTextFieldProps;
  MuiPickersInputBase: PickersInputBaseProps;
  MuiPickersInput: PickersInputProps;
  MuiPickersFilledInput: PickersFilledInputProps;
  MuiPickersOutlinedInput: PickersOutlinedInputProps;
  MuiPickersSectionList: PickersSectionListProps;
}
declare module '@mui/material/styles' {
  interface ComponentsPropsList extends PickersComponentsPropsList {}
}
export {};