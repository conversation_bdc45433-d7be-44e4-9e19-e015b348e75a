<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #9e655c;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #8a5a52;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>Login API Test</h1>
    <p>Test the login API to verify the fix works correctly.</p>
    
    <form id="loginForm">
        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" name="email" required>
        </div>
        
        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" name="password" required>
        </div>
        
        <button type="submit">Test Login</button>
    </form>
    
    <div id="result"></div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            try {
                console.log('Testing login with:', { email, password: '***' });
                
                const response = await fetch('/api/Auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password }),
                });
                
                console.log('Response status:', response.status);
                
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.message || errorData.error || 'Login failed');
                }
                
                const result = await response.json();
                console.log('Login response:', result);
                
                // Test the fix: check if Token exists directly
                if (result.Token) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ SUCCESS! 
Login response structure is correct:
- Token: ${result.Token.substring(0, 50)}...
- RefreshToken: ${result.RefreshToken ? result.RefreshToken.substring(0, 20) + '...' : 'Not provided'}

The fix is working! Frontend should now be able to extract the token correctly.`;
                } else if (result.error) {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ LOGIN ERROR: ${result.error}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ UNEXPECTED RESPONSE STRUCTURE:
${JSON.stringify(result, null, 2)}`;
                }
                
            } catch (error) {
                console.error('Login test error:', error);
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ ERROR: ${error.message}`;
            }
        });
    </script>
</body>
</html>
