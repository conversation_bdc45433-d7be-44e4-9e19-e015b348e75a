﻿using JucieAndFlower.Data.Enities.Order;
using JucieAndFlower.Data.Models;
using JucieAndFlower.Service.Interface;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;

namespace JucieAndFlower.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class OrdersController : Controller
    {
        private readonly IOrderService _orderService;
        private readonly IVNPayService _vnpayService;
        private readonly IPaymentService _paymentService;
        public OrdersController(IOrderService orderService, IVNPayService vnpayService, IPaymentService paymentService)
        {
            _orderService = orderService;
            _vnpayService = vnpayService;
            _paymentService = paymentService;
        }


        [HttpGet]
        public async Task<IActionResult> GetUserOrders()
        {
            try
            {
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userIdClaim) || !int.TryParse(userIdClaim, out int userId))
                {
                    return Unauthorized(new { Success = false, Message = "User ID claim not found or invalid" });
                }

                var orders = await _orderService.GetOrdersByUserIdAsync(userId);
                return Ok(orders);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting user orders: {ex.Message}");
                return StatusCode(500, new { Success = false, Message = "Internal server error" });
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetOrderById(int id)
        {
            try
            {
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userIdClaim) || !int.TryParse(userIdClaim, out int userId))
                {
                    return Unauthorized(new { Success = false, Message = "User ID claim not found or invalid" });
                }

                var order = await _orderService.GetOrderByIdAsync(id);
                if (order == null)
                {
                    return NotFound(new { Success = false, Message = "Order not found" });
                }

                if (order.UserId != userId)
                {
                    return Forbid();
                }

                return Ok(new { Success = true, Data = order });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting order by ID: {ex.Message}");
                return StatusCode(500, new { Success = false, Message = "Internal server error" });
            }
        }

        [HttpGet("payment-return")]
        [AllowAnonymous]
        public async Task<IActionResult> PaymentReturn()
        {
            try
            {
                var response = _vnpayService.GetReturnData(Request.Query);
                Console.WriteLine("VNPay Response OrderId: " + response.OrderId);
                Console.WriteLine("VNPay Response Success: " + response.Success);

                var order = await _orderService.GetOrderByIdAsync(response.OrderId);
                if (order == null)
                {
                  
                    return Redirect($"http://localhost:5173/payment-result?success=false&message=Order not found&orderId={response.OrderId}");
                }

                var payment = new Payment
                {
                    OrderId = order.OrderId,
                    PaymentMethod = "VNPay",
                    PaidAmount = order.FinalAmount,
                    PaymentDate = DateTime.Now,
                    Status = response.Success ? "Paid" : "Cancel"
                };

                await _paymentService.AddPaymentAsync(payment);

                if (response.Success)
                {
                    await _orderService.MarkOrderAsCompleteAsync(order.OrderId);

                    return Redirect($"http://localhost:5173/payment-result?success=true&orderId={order.OrderId}&amount={order.FinalAmount}&message=Payment successful");
                }
                else
                {
                    await _orderService.MarkOrderAsCanceledAsync(order.OrderId);

                    return Redirect($"http://localhost:5173/payment-result?success=false&orderId={order.OrderId}&message=Payment failed or canceled");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in PaymentReturn: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");

                return Redirect($"http://localhost:5173/payment-result?success=false&message=Payment processing error");
            }
        }


        [HttpPost("from-cart")]
        public async Task<IActionResult> CreateOrderFromCart([FromBody] OrderFromCartDTO dto)
        {
            try
            {
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userIdClaim) || !int.TryParse(userIdClaim, out int userId))
                {
                    return Unauthorized(new { Success = false, Message = "User ID claim not found or invalid" });
                }

                if (dto == null)
                {
                    return BadRequest(new { Success = false, Message = "Invalid order data" });
                }

                if (string.IsNullOrWhiteSpace(dto.DeliveryAddress))
                {
                    return BadRequest(new { Success = false, Message = "Delivery address is required" });
                }

                if (dto.SelectedCartItemIds == null || !dto.SelectedCartItemIds.Any())
                {
                    return BadRequest(new { Success = false, Message = "No cart items selected" });
                }

                dto.UserId = userId;
                var order = await _orderService.CreateOrderFromCartAsync(dto);

                if (order?.FinalAmount == null)
                {
                    return BadRequest(new { Success = false, Message = "Failed to create order" });
                }

                decimal totalAmount = (decimal)order.FinalAmount;
                var paymentUrl = _vnpayService.CreatePaymentUrl(order.OrderId, totalAmount, HttpContext);

                return Ok(new
                {
                    Success = true,
                    OrderId = order.OrderId,
                    PaymentUrl = paymentUrl
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error creating order: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");

                return StatusCode(500, new {
                    Success = false,
                    Message = ex.Message.Contains("Không tìm thấy sản phẩm") ? ex.Message : "Internal server error occurred while creating order"
                });
            }
        }


    }
}
