﻿using JucieAndFlower.Data.Enities.Order;
using JucieAndFlower.Data.Models;
using JucieAndFlower.Service.Interface;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;

namespace JucieAndFlower.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class OrdersController : Controller
    {
        private readonly IOrderService _orderService;
        private readonly IVNPayService _vnpayService;
        private readonly IPaymentService _paymentService;
        public OrdersController(IOrderService orderService, IVNPayService vnpayService, IPaymentService paymentService)
        {
            _orderService = orderService;
            _vnpayService = vnpayService;
            _paymentService = paymentService;
        }


        [HttpGet]
        public async Task<IActionResult> GetOrderById(int id)
        {
            int userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? throw new Exception("User ID claim not found"));
            var order = await _orderService.GetOrderByIdAsync(id);
            if (order == null) return NotFound();
            return Ok(order);
        }

        // Dùng cho testing API chứ không redirect nữa
        [HttpGet("payment-return")]
        public async Task<IActionResult> PaymentReturn()
        {
            var response = _vnpayService.GetReturnData(Request.Query);
            Console.WriteLine("VNPay Response OrderId: " + response.OrderId);

            // Lấy thông tin đơn hàng
            var order = await _orderService.GetOrderByIdAsync(response.OrderId);
            if (order == null)
            {
                return NotFound(new { Success = false, Message = "Order not found." });
            }

            // Tạo bản ghi thanh toán
            var payment = new Payment
            {
                OrderId = order.OrderId,
                PaymentMethod = "VNPay",
                PaidAmount = order.FinalAmount,
                PaymentDate = DateTime.Now,
                Status = response.Success ? "Paid" : "Cancel"
            };

            await _paymentService.AddPaymentAsync(payment);
            if (!response.Success)
            {
                await _orderService.MarkOrderAsCanceledAsync(order.OrderId);
            }
            // Nếu thành công, cập nhật trạng thái đơn hàng
            if (response.Success)
            {
                await _orderService.MarkOrderAsCompleteAsync(order.OrderId);

                return Ok(new
                {
                    Success = true,
                    Message = "Payment successful",
                    OrderId = order.OrderId
                });
            }

            // Nếu thất bại, chỉ trả kết quả
            return BadRequest(new
            {
                Success = false,
                Message = "Payment failed or canceled",
                OrderId = order.OrderId
            });

        

        }


        [HttpPost("from-cart")]
        public async Task<IActionResult> CreateOrderFromCart([FromBody] OrderFromCartDTO dto)
        {
            try
            {
                // Validate user authentication
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userIdClaim) || !int.TryParse(userIdClaim, out int userId))
                {
                    return Unauthorized(new { Success = false, Message = "User ID claim not found or invalid" });
                }

                // Validate input
                if (dto == null)
                {
                    return BadRequest(new { Success = false, Message = "Invalid order data" });
                }

                if (string.IsNullOrWhiteSpace(dto.DeliveryAddress))
                {
                    return BadRequest(new { Success = false, Message = "Delivery address is required" });
                }

                if (dto.SelectedCartItemIds == null || !dto.SelectedCartItemIds.Any())
                {
                    return BadRequest(new { Success = false, Message = "No cart items selected" });
                }

                dto.UserId = userId;
                var order = await _orderService.CreateOrderFromCartAsync(dto);

                if (order?.FinalAmount == null)
                {
                    return BadRequest(new { Success = false, Message = "Failed to create order" });
                }

                decimal totalAmount = (decimal)order.FinalAmount;
                var paymentUrl = _vnpayService.CreatePaymentUrl(order.OrderId, totalAmount, HttpContext);

                return Ok(new
                {
                    Success = true,
                    OrderId = order.OrderId,
                    PaymentUrl = paymentUrl
                });
            }
            catch (Exception ex)
            {
                // Log the exception (you might want to use a proper logging framework)
                Console.WriteLine($"Error creating order: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");

                return StatusCode(500, new {
                    Success = false,
                    Message = ex.Message.Contains("Không tìm thấy sản phẩm") ? ex.Message : "Internal server error occurred while creating order"
                });
            }
        }


    }
}
