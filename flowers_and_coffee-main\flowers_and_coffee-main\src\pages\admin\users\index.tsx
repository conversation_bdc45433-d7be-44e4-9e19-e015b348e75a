import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Box,
  Chip,
  Alert,
  CircularProgress,
  TextField,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  Card,
  CardContent,
  Avatar,
} from "@mui/material";
import {
  Visibility,
  Edit,
  Block,
  CheckCircle,
  Refresh,
  PersonAdd,
} from "@mui/icons-material";
import { toast } from "react-toastify";

interface User {
  id: string;
  fullName: string;
  email: string;
  phone: string;
  roleId: number;
  roleName: string;
  status: string;
  createdAt: string;
  lastLogin: string;
}

const AdminUsers: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [detailDial<PERSON><PERSON><PERSON>, setDetail<PERSON>ialogOpen] = useState(false);
  const [roleFilter, setRoleFilter] = useState("all");

  // Mock data for demonstration
  useEffect(() => {
    const mockUsers: User[] = [
      {
        id: "1",
        fullName: "Nguyễn Văn A",
        email: "<EMAIL>",
        phone: "0123456789",
        roleId: 1,
        roleName: "Khách hàng",
        status: "active",
        createdAt: "2024-01-15T10:30:00Z",
        lastLogin: "2024-01-21T09:15:00Z",
      },
      {
        id: "2",
        fullName: "Trần Thị B",
        email: "<EMAIL>",
        phone: "0987654321",
        roleId: 1,
        roleName: "Khách hàng",
        status: "active",
        createdAt: "2024-01-16T14:20:00Z",
        lastLogin: "2024-01-20T16:45:00Z",
      },
      {
        id: "3",
        fullName: "Lê Văn C",
        email: "<EMAIL>",
        phone: "0555666777",
        roleId: 2,
        roleName: "Nhân viên",
        status: "active",
        createdAt: "2024-01-10T08:00:00Z",
        lastLogin: "2024-01-21T08:30:00Z",
      },
      {
        id: "4",
        fullName: "Admin System",
        email: "<EMAIL>",
        phone: "0111222333",
        roleId: 4,
        roleName: "Quản trị viên",
        status: "active",
        createdAt: "2024-01-01T00:00:00Z",
        lastLogin: "2024-01-21T10:00:00Z",
      },
    ];

    setTimeout(() => {
      setUsers(mockUsers);
      setLoading(false);
    }, 1000);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active": return "success";
      case "inactive": return "default";
      case "blocked": return "error";
      default: return "default";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "active": return "Hoạt động";
      case "inactive": return "Không hoạt động";
      case "blocked": return "Bị khóa";
      default: return status;
    }
  };

  const getRoleColor = (roleId: number) => {
    switch (roleId) {
      case 4: return "#f44336"; // Admin - Red
      case 2: return "#ff9800"; // Staff - Orange
      case 1: return "#4caf50"; // Customer - Green
      default: return "#9e9e9e";
    }
  };

  const handleStatusChange = (userId: string, newStatus: string) => {
    setUsers(prev => 
      prev.map(user => 
        user.id === userId ? { ...user, status: newStatus } : user
      )
    );
    toast.success(`Đã cập nhật trạng thái người dùng`);
  };

  const handleViewDetails = (user: User) => {
    setSelectedUser(user);
    setDetailDialogOpen(true);
  };

  const filteredUsers = roleFilter === "all" 
    ? users 
    : users.filter(user => user.roleId.toString() === roleFilter);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={4}>
        <Typography
          variant="h4"
          sx={{
            fontWeight: 700,
            color: "#333",
          }}
        >
          👥 Quản lý người dùng
        </Typography>
        <Box display="flex" gap={2}>
          <Button
            variant="contained"
            startIcon={<PersonAdd />}
            sx={{
              bgcolor: "#4caf50",
              "&:hover": { bgcolor: "#45a049" },
            }}
          >
            Thêm người dùng
          </Button>
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={() => window.location.reload()}
            sx={{
              borderColor: "#9e655c",
              color: "#9e655c",
              "&:hover": {
                borderColor: "#8a5a52",
                bgcolor: "rgba(158, 101, 92, 0.04)",
              },
            }}
          >
            Làm mới
          </Button>
        </Box>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ borderRadius: 3, border: "1px solid #f0f0f0" }}>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                Tổng người dùng
              </Typography>
              <Typography variant="h4" fontWeight="600" color="#333">
                {users.length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ borderRadius: 3, border: "1px solid #f0f0f0" }}>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                Khách hàng
              </Typography>
              <Typography variant="h4" fontWeight="600" color="#4caf50">
                {users.filter(u => u.roleId === 1).length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ borderRadius: 3, border: "1px solid #f0f0f0" }}>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                Nhân viên
              </Typography>
              <Typography variant="h4" fontWeight="600" color="#ff9800">
                {users.filter(u => u.roleId === 2).length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ borderRadius: 3, border: "1px solid #f0f0f0" }}>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                Hoạt động
              </Typography>
              <Typography variant="h4" fontWeight="600" color="#2196f3">
                {users.filter(u => u.status === "active").length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filter */}
      <Box mb={3}>
        <TextField
          select
          label="Lọc theo vai trò"
          value={roleFilter}
          onChange={(e) => setRoleFilter(e.target.value)}
          size="small"
          sx={{ minWidth: 200 }}
        >
          <MenuItem value="all">Tất cả</MenuItem>
          <MenuItem value="1">Khách hàng</MenuItem>
          <MenuItem value="2">Nhân viên</MenuItem>
          <MenuItem value="4">Quản trị viên</MenuItem>
        </TextField>
      </Box>

      {/* Users Table */}
      <Paper sx={{ borderRadius: 3, border: "1px solid #f0f0f0" }}>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell sx={{ fontWeight: 600 }}>Người dùng</TableCell>
                <TableCell sx={{ fontWeight: 600 }}>Email</TableCell>
                <TableCell sx={{ fontWeight: 600 }}>Số điện thoại</TableCell>
                <TableCell sx={{ fontWeight: 600 }}>Vai trò</TableCell>
                <TableCell sx={{ fontWeight: 600 }}>Trạng thái</TableCell>
                <TableCell sx={{ fontWeight: 600 }}>Đăng nhập cuối</TableCell>
                <TableCell align="center" sx={{ fontWeight: 600 }}>Thao tác</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredUsers.map((user) => (
                <TableRow key={user.id} hover>
                  <TableCell>
                    <Box display="flex" alignItems="center" gap={2}>
                      <Avatar
                        sx={{
                          bgcolor: getRoleColor(user.roleId),
                          width: 40,
                          height: 40,
                        }}
                      >
                        {user.fullName.charAt(0)}
                      </Avatar>
                      <Box>
                        <Typography variant="body2" fontWeight="600">
                          {user.fullName}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          ID: {user.id}
                        </Typography>
                      </Box>
                    </Box>
                  </TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>{user.phone}</TableCell>
                  <TableCell>
                    <Chip
                      label={user.roleName}
                      size="small"
                      sx={{
                        bgcolor: `${getRoleColor(user.roleId)}20`,
                        color: getRoleColor(user.roleId),
                      }}
                    />
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={getStatusText(user.status)}
                      color={getStatusColor(user.status) as any}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    {new Date(user.lastLogin).toLocaleDateString("vi-VN")}
                  </TableCell>
                  <TableCell align="center">
                    <IconButton
                      size="small"
                      onClick={() => handleViewDetails(user)}
                      sx={{ mr: 1 }}
                    >
                      <Visibility />
                    </IconButton>
                    <IconButton
                      size="small"
                      sx={{ mr: 1, color: "#ff9800" }}
                    >
                      <Edit />
                    </IconButton>
                    {user.status === "active" ? (
                      <IconButton
                        size="small"
                        onClick={() => handleStatusChange(user.id, "blocked")}
                        sx={{ color: "#f44336" }}
                      >
                        <Block />
                      </IconButton>
                    ) : (
                      <IconButton
                        size="small"
                        onClick={() => handleStatusChange(user.id, "active")}
                        sx={{ color: "#4caf50" }}
                      >
                        <CheckCircle />
                      </IconButton>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      {/* User Detail Dialog */}
      <Dialog
        open={detailDialogOpen}
        onClose={() => setDetailDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Chi tiết người dùng</DialogTitle>
        <DialogContent>
          {selectedUser && (
            <Box>
              <Box display="flex" alignItems="center" gap={2} mb={3}>
                <Avatar
                  sx={{
                    bgcolor: getRoleColor(selectedUser.roleId),
                    width: 60,
                    height: 60,
                  }}
                >
                  {selectedUser.fullName.charAt(0)}
                </Avatar>
                <Box>
                  <Typography variant="h6">{selectedUser.fullName}</Typography>
                  <Chip
                    label={selectedUser.roleName}
                    size="small"
                    sx={{
                      bgcolor: `${getRoleColor(selectedUser.roleId)}20`,
                      color: getRoleColor(selectedUser.roleId),
                    }}
                  />
                </Box>
              </Box>

              <Typography variant="body1" gutterBottom>
                <strong>Email:</strong> {selectedUser.email}
              </Typography>
              <Typography variant="body1" gutterBottom>
                <strong>Số điện thoại:</strong> {selectedUser.phone}
              </Typography>
              <Typography variant="body1" gutterBottom>
                <strong>Trạng thái:</strong> {getStatusText(selectedUser.status)}
              </Typography>
              <Typography variant="body1" gutterBottom>
                <strong>Ngày tạo:</strong> {new Date(selectedUser.createdAt).toLocaleString("vi-VN")}
              </Typography>
              <Typography variant="body1">
                <strong>Đăng nhập cuối:</strong> {new Date(selectedUser.lastLogin).toLocaleString("vi-VN")}
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDetailDialogOpen(false)}>Đóng</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AdminUsers;
