import React, { useState, useEffect } from "react";
import {
  Container,
  Paper,
  Typography,
  TextField,
  Button,
  Grid,
  Box,
  Avatar,
  Divider,
  Alert,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from "@mui/material";
import { Person, Edit, Save, Cancel } from "@mui/icons-material";
import { useAppSelector, useAppDispatch } from "../../stores/hooks";
import { updateProfile } from "../../stores/reducers/Authentication";
import { authAPI } from "../../services/api";
import { toast } from "react-toastify";

const ProfilePage = () => {
  const { user } = useAppSelector((state) => state.Authentication);
  const dispatch = useAppDispatch();

  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  const [formData, setFormData] = useState({
    fullName: user?.fullName || "",
    email: user?.email || "",
    phone: user?.phone || "",
    address: user?.address || "",
    gender: user?.gender || "",
    birthDate: user?.birthDate ? user.birthDate.split('T')[0] : "",
  });

  const [passwordData, setPasswordData] = useState({
    oldPassword: "",
    newPassword: "",
    confirmPassword: "",
  });

  const [showPasswordForm, setShowPasswordForm] = useState(false);

  useEffect(() => {
    if (user) {
      setFormData({
        fullName: user.fullName || "",
        email: user.email || "",
        phone: user.phone || "",
        address: user.address || "",
        gender: user.gender || "",
        birthDate: user.birthDate ? user.birthDate.split('T')[0] : "",
      });
    }
  }, [user]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handlePasswordChange = (field: string, value: string) => {
    setPasswordData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = async () => {
    setError("");
    setLoading(true);

    try {
      // Validate password if changing
      if (showPasswordForm) {
        if (!passwordData.oldPassword || !passwordData.newPassword || !passwordData.confirmPassword) {
          setError("Vui lòng điền đầy đủ thông tin mật khẩu");
          setLoading(false);
          return;
        }

        if (passwordData.newPassword !== passwordData.confirmPassword) {
          setError("Mật khẩu mới và xác nhận mật khẩu không khớp");
          setLoading(false);
          return;
        }

        if (passwordData.newPassword.length < 6) {
          setError("Mật khẩu mới phải có ít nhất 6 ký tự");
          setLoading(false);
          return;
        }
      }

      const updateData = {
        fullName: formData.fullName,
        phone: formData.phone,
        address: formData.address,
        gender: formData.gender,
        birthDate: formData.birthDate,
        ...(showPasswordForm && passwordData.newPassword ? {
          oldPassword: passwordData.oldPassword,
          newPassword: passwordData.newPassword,
          confirmPassword: passwordData.confirmPassword
        } : {})
      };

      // Call update API (returns success message)
      await authAPI.updateProfile(updateData);

      // Get fresh user data after update
      const updatedUser = await authAPI.getProfile();
      dispatch(updateProfile(updatedUser));

      setIsEditing(false);
      setShowPasswordForm(false);
      setPasswordData({ oldPassword: "", newPassword: "", confirmPassword: "" });
      toast.success("Cập nhật thông tin thành công!");
    } catch (error: any) {
      const errorMessage = error.message || "Có lỗi xảy ra khi cập nhật thông tin";
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    setShowPasswordForm(false);
    setError("");
    setPasswordData({ oldPassword: "", newPassword: "", confirmPassword: "" });

    if (user) {
      setFormData({
        fullName: user.fullName || "",
        email: user.email || "",
        phone: user.phone || "",
        address: user.address || "",
        gender: user.gender || "",
        birthDate: user.birthDate ? user.birthDate.split('T')[0] : "",
      });
    }
  };

  if (!user) {
    return (
      <Container maxWidth="md" sx={{ mt: 4 }}>
        <Alert severity="warning">
          Vui lòng đăng nhập để xem thông tin cá nhân
        </Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="md" sx={{ mt: 4, mb: 4 }}>
      {/* Header Section */}
      <Box sx={{ mb: 4 }}>
        <Box display="flex" alignItems="center" mb={2}>
          <Avatar
            sx={{
              width: 80,
              height: 80,
              mr: 3,
              bgcolor: "#9e655c",
            }}
          >
            <Person sx={{ fontSize: 40 }} />
          </Avatar>
          <Box>
            <Typography variant="h4" gutterBottom fontWeight="600" color="#333">
              {user?.fullName || "Người dùng"}
            </Typography>
            <Typography variant="body1" color="text.secondary">
              {user?.email}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Quản lý thông tin tài khoản của bạn
            </Typography>
          </Box>
        </Box>
      </Box>

      {/* Main Content */}
      <Paper elevation={1} sx={{ p: 4, borderRadius: 2, border: '1px solid #e0e0e0' }}>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
          <TextField
            fullWidth
            label="Họ và tên"
            value={formData.fullName}
            onChange={(e) => handleInputChange("fullName", e.target.value)}
            disabled={!isEditing}
            variant={isEditing ? "outlined" : "filled"}
            size="small"
            sx={{
              "& .MuiOutlinedInput-root": {
                borderRadius: isEditing ? "20px" : "4px",
              },
            }}
          />

          <TextField
            fullWidth
            label="Email"
            value={formData.email}
            disabled={true}
            variant="filled"
            size="small"
            helperText="Email không thể thay đổi"
            sx={{
              "& .MuiFilledInput-root": {
                borderRadius: "4px",
              },
            }}
          />

          <TextField
            fullWidth
            label="Số điện thoại"
            value={formData.phone}
            onChange={(e) => handleInputChange("phone", e.target.value)}
            disabled={!isEditing}
            variant={isEditing ? "outlined" : "filled"}
            size="small"
            sx={{
              "& .MuiOutlinedInput-root": {
                borderRadius: isEditing ? "20px" : "4px",
              },
            }}
          />

          <FormControl
            fullWidth
            disabled={!isEditing}
            size="small"
            sx={{
              "& .MuiOutlinedInput-root": {
                borderRadius: isEditing ? "20px" : "4px",
              },
            }}
          >
            <InputLabel>Giới tính</InputLabel>
            <Select
              value={formData.gender}
              label="Giới tính"
              onChange={(e) => handleInputChange("gender", e.target.value)}
              variant={isEditing ? "outlined" : "filled"}
            >
              <MenuItem value="Male">Nam</MenuItem>
              <MenuItem value="Female">Nữ</MenuItem>
              <MenuItem value="Other">Khác</MenuItem>
            </Select>
          </FormControl>

          <TextField
            fullWidth
            label="Ngày sinh"
            type="date"
            value={formData.birthDate}
            onChange={(e) => handleInputChange("birthDate", e.target.value)}
            disabled={!isEditing}
            variant={isEditing ? "outlined" : "filled"}
            size="small"
            InputLabelProps={{ shrink: true }}
            sx={{
              "& .MuiOutlinedInput-root": {
                borderRadius: isEditing ? "20px" : "4px",
              },
            }}
          />

          <TextField
            fullWidth
            label="Địa chỉ"
            value={formData.address}
            onChange={(e) => handleInputChange("address", e.target.value)}
            disabled={!isEditing}
            variant={isEditing ? "outlined" : "filled"}
            size="small"
            multiline
            rows={2}
            sx={{
              "& .MuiOutlinedInput-root": {
                borderRadius: isEditing ? "20px" : "4px",
              },
            }}
          />
        </Box>

        {isEditing && (
          <Box sx={{ mt: 3 }}>
            <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
              <Typography variant="h6" fontWeight="600" color="#333">Đổi mật khẩu</Typography>
              <Button
                onClick={() => setShowPasswordForm(!showPasswordForm)}
                variant="outlined"
                size="small"
                sx={{
                  borderColor: "#9e655c",
                  color: "#9e655c",
                  borderRadius: "20px",
                  "&:hover": {
                    borderColor: "#8a5a52",
                    bgcolor: "rgba(158, 101, 92, 0.04)",
                  },
                }}
              >
                {showPasswordForm ? "Hủy" : "Đổi mật khẩu"}
              </Button>
            </Box>

            {showPasswordForm && (
              <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
                <TextField
                  fullWidth
                  label="Mật khẩu cũ"
                  type="password"
                  value={passwordData.oldPassword}
                  onChange={(e) => handlePasswordChange("oldPassword", e.target.value)}
                  size="small"
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      borderRadius: "20px",
                    },
                  }}
                />
                <TextField
                  fullWidth
                  label="Mật khẩu mới"
                  type="password"
                  value={passwordData.newPassword}
                  onChange={(e) => handlePasswordChange("newPassword", e.target.value)}
                  size="small"
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      borderRadius: "20px",
                    },
                  }}
                />
                <TextField
                  fullWidth
                  label="Xác nhận mật khẩu"
                  type="password"
                  value={passwordData.confirmPassword}
                  onChange={(e) => handlePasswordChange("confirmPassword", e.target.value)}
                  size="small"
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      borderRadius: "20px",
                    },
                  }}
                />
              </Box>
            )}
          </Box>
        )}

        <Box mt={4} display="flex" gap={2} justifyContent="center">
          {!isEditing ? (
            <Button
              variant="contained"
              startIcon={<Edit />}
              onClick={() => setIsEditing(true)}
              size="small"
              sx={{
                bgcolor: "#D9D9D9",
                color: "black",
                borderRadius: "20px",
                px: 4,
                py: 1,
                "&:hover": {
                  bgcolor: "#d5d5d5",
                },
              }}
            >
              Chỉnh sửa
            </Button>
          ) : (
            <>
              <Button
                variant="outlined"
                startIcon={<Cancel />}
                onClick={handleCancel}
                disabled={loading}
                size="small"
                sx={{
                  borderColor: "#9e655c",
                  color: "#9e655c",
                  borderRadius: "20px",
                  px: 3,
                  py: 1,
                  "&:hover": {
                    borderColor: "#8a5a52",
                    bgcolor: "rgba(158, 101, 92, 0.04)",
                  },
                }}
              >
                Hủy
              </Button>
              <Button
                variant="contained"
                startIcon={loading ? <CircularProgress size={20} /> : <Save />}
                onClick={handleSave}
                disabled={loading}
                size="small"
                sx={{
                  bgcolor: "#D9D9D9",
                  color: "black",
                  borderRadius: "20px",
                  px: 3,
                  py: 1,
                  "&:hover": {
                    bgcolor: "#d5d5d5",
                  },
                }}
              >
                Lưu thay đổi
              </Button>
            </>
          )}
        </Box>
      </Paper>
    </Container>
  );
};

export default ProfilePage;
