<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Checkout Flow</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .step {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .step h3 {
            margin-top: 0;
            color: #9e655c;
        }
        button {
            background: #9e655c;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #8a5a52;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            background: #e8f5e8;
            border: 1px solid #4caf50;
        }
        .error {
            background: #ffebee;
            border: 1px solid #f44336;
            color: #d32f2f;
        }
        pre {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
        }
        .status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.success { background: #4caf50; color: white; }
        .status.error { background: #f44336; color: white; }
        .status.pending { background: #ff9800; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌸 Test Checkout Flow - Flowers & Coffee</h1>
        <p>Trang này giúp test toàn bộ flow checkout từ đăng nhập đến thanh toán VNPay</p>

        <div class="step">
            <h3>1. Đăng nhập</h3>
            <p>Đăng nhập với tài khoản test:</p>
            <input type="email" id="email" placeholder="Email" value="<EMAIL>" style="width: 200px; padding: 8px; margin: 5px;">
            <input type="password" id="password" placeholder="Password" value="123456" style="width: 200px; padding: 8px; margin: 5px;">
            <br>
            <button onclick="login()">Đăng nhập</button>
            <div id="loginResult"></div>
        </div>

        <div class="step">
            <h3>2. Thêm sản phẩm vào giỏ hàng</h3>
            <p>Thêm một số sản phẩm để test:</p>
            <button onclick="addToCart(1, 2)">Thêm Hoa hồng (ID: 1, SL: 2)</button>
            <button onclick="addToCart(2, 1)">Thêm Cà phê (ID: 2, SL: 1)</button>
            <button onclick="getCart()">Xem giỏ hàng</button>
            <div id="cartResult"></div>
        </div>

        <div class="step">
            <h3>3. Checkout</h3>
            <p>Tạo đơn hàng và chuyển đến VNPay:</p>
            <input type="text" id="address" placeholder="Địa chỉ giao hàng" value="123 Đường ABC, Quận 1, TP.HCM" style="width: 300px; padding: 8px; margin: 5px;">
            <br>
            <input type="text" id="note" placeholder="Ghi chú (tùy chọn)" value="Giao hàng buổi sáng" style="width: 300px; padding: 8px; margin: 5px;">
            <br>
            <button onclick="checkout()">Đặt hàng và thanh toán</button>
            <div id="checkoutResult"></div>
        </div>

        <div class="step">
            <h3>4. Kiểm tra đơn hàng</h3>
            <p>Xem danh sách đơn hàng:</p>
            <button onclick="getOrders()">Lấy danh sách đơn hàng</button>
            <button onclick="getOrderDetail()">Xem chi tiết đơn hàng cuối</button>
            <div id="ordersResult"></div>
        </div>

        <div class="step">
            <h3>5. Test Links</h3>
            <p>Các link để test:</p>
            <a href="http://localhost:5173" target="_blank">Frontend (localhost:5173)</a> |
            <a href="http://localhost:5173/auth/login" target="_blank">Login Page</a> |
            <a href="http://localhost:5173/cart" target="_blank">Cart Page</a> |
            <a href="http://localhost:5173/checkout" target="_blank">Checkout Page</a> |
            <a href="http://localhost:5173/orders" target="_blank">Orders Page</a>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5173/api';
        let authToken = localStorage.getItem('token') || '';
        let lastOrderId = null;

        function showResult(elementId, content, isError = false) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="result ${isError ? 'error' : ''}">${content}</div>`;
        }

        function showStatus(elementId, status, message) {
            const element = document.getElementById(elementId);
            const statusClass = status === 'success' ? 'success' : status === 'error' ? 'error' : 'pending';
            element.innerHTML = `<div class="result"><span class="status ${statusClass}">${status.toUpperCase()}</span> ${message}</div>`;
        }

        async function makeRequest(url, options = {}) {
            const headers = {
                'Content-Type': 'application/json',
                ...(authToken && { 'Authorization': `Bearer ${authToken}` }),
                ...options.headers
            };

            try {
                const response = await fetch(API_BASE + url, {
                    ...options,
                    headers
                });

                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${data.message || data.error || 'Unknown error'}`);
                }

                return data;
            } catch (error) {
                console.error('Request failed:', error);
                throw error;
            }
        }

        async function login() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;

            showStatus('loginResult', 'pending', 'Đang đăng nhập...');

            try {
                const result = await fetch(API_BASE + '/Auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ email, password })
                });

                const data = await result.json();

                if (result.ok && data.token) {
                    authToken = data.token;
                    localStorage.setItem('token', authToken);
                    showStatus('loginResult', 'success', `Đăng nhập thành công! User: ${data.user?.fullName || email}`);
                } else {
                    throw new Error(data.message || 'Login failed');
                }
            } catch (error) {
                showStatus('loginResult', 'error', `Lỗi đăng nhập: ${error.message}`);
            }
        }

        async function addToCart(productId, quantity) {
            showStatus('cartResult', 'pending', `Đang thêm sản phẩm ${productId} vào giỏ hàng...`);

            try {
                await makeRequest('/Cart', {
                    method: 'POST',
                    body: JSON.stringify({ productId, quantity })
                });

                showStatus('cartResult', 'success', `Đã thêm sản phẩm ${productId} (SL: ${quantity}) vào giỏ hàng`);
            } catch (error) {
                showStatus('cartResult', 'error', `Lỗi thêm vào giỏ hàng: ${error.message}`);
            }
        }

        async function getCart() {
            showStatus('cartResult', 'pending', 'Đang lấy giỏ hàng...');

            try {
                const cart = await makeRequest('/Cart');
                showResult('cartResult', `
                    <strong>Giỏ hàng hiện tại:</strong>
                    <pre>${JSON.stringify(cart, null, 2)}</pre>
                `);
            } catch (error) {
                showStatus('cartResult', 'error', `Lỗi lấy giỏ hàng: ${error.message}`);
            }
        }

        async function checkout() {
            const address = document.getElementById('address').value;
            const note = document.getElementById('note').value;

            if (!address.trim()) {
                showStatus('checkoutResult', 'error', 'Vui lòng nhập địa chỉ giao hàng');
                return;
            }

            showStatus('checkoutResult', 'pending', 'Đang tạo đơn hàng...');

            try {
                const result = await makeRequest('/Orders/from-cart', {
                    method: 'POST',
                    body: JSON.stringify({
                        deliveryAddress: address,
                        note: note,
                        promotionCode: ''
                    })
                });

                lastOrderId = result.orderId || result.OrderId;

                showResult('checkoutResult', `
                    <strong>Đơn hàng đã tạo thành công!</strong><br>
                    Order ID: ${lastOrderId}<br>
                    Payment URL: <a href="${result.paymentUrl || result.PaymentUrl}" target="_blank">Thanh toán VNPay</a><br>
                    <pre>${JSON.stringify(result, null, 2)}</pre>
                `);
            } catch (error) {
                showStatus('checkoutResult', 'error', `Lỗi checkout: ${error.message}`);
            }
        }

        async function getOrders() {
            showStatus('ordersResult', 'pending', 'Đang lấy danh sách đơn hàng...');

            try {
                const orders = await makeRequest('/Orders');
                showResult('ordersResult', `
                    <strong>Danh sách đơn hàng:</strong>
                    <pre>${JSON.stringify(orders, null, 2)}</pre>
                `);

                if (orders && orders.length > 0) {
                    lastOrderId = orders[0].orderId || orders[0].OrderId;
                }
            } catch (error) {
                showStatus('ordersResult', 'error', `Lỗi lấy đơn hàng: ${error.message}`);
            }
        }

        async function getOrderDetail() {
            if (!lastOrderId) {
                showStatus('ordersResult', 'error', 'Chưa có Order ID. Vui lòng tạo đơn hàng hoặc lấy danh sách đơn hàng trước.');
                return;
            }

            showStatus('ordersResult', 'pending', `Đang lấy chi tiết đơn hàng ${lastOrderId}...`);

            try {
                const order = await makeRequest(`/Orders/${lastOrderId}`);
                showResult('ordersResult', `
                    <strong>Chi tiết đơn hàng ${lastOrderId}:</strong>
                    <pre>${JSON.stringify(order, null, 2)}</pre>
                `);
            } catch (error) {
                showStatus('ordersResult', 'error', `Lỗi lấy chi tiết đơn hàng: ${error.message}`);
            }
        }

        // Auto-load token on page load
        if (authToken) {
            showStatus('loginResult', 'success', 'Đã có token trong localStorage');
        }
    </script>
</body>
</html>
