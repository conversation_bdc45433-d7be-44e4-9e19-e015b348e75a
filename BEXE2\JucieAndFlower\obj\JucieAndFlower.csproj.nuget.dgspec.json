{"format": 1, "restore": {"E:\\CODE-THUE\\DATN\\exe\\exe\\BEXE2\\JucieAndFlower\\JucieAndFlower.csproj": {}}, "projects": {"E:\\CODE-THUE\\DATN\\exe\\exe\\BEXE2\\JucieAndFlower.Data\\JucieAndFlower.Data.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\CODE-THUE\\DATN\\exe\\exe\\BEXE2\\JucieAndFlower.Data\\JucieAndFlower.Data.csproj", "projectName": "JucieAndFlower.Data", "projectPath": "E:\\CODE-THUE\\DATN\\exe\\exe\\BEXE2\\JucieAndFlower.Data\\JucieAndFlower.Data.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\CODE-THUE\\DATN\\exe\\exe\\BEXE2\\JucieAndFlower.Data\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Users\\<USER>\\Documents\\Infragistics\\NuGet\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.15, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.15, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.15, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.0.15, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.15, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.2, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[8.9.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.100/PortableRuntimeIdentifierGraph.json"}}}, "E:\\CODE-THUE\\DATN\\exe\\exe\\BEXE2\\JucieAndFlower.Service\\JucieAndFlower.Service.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\CODE-THUE\\DATN\\exe\\exe\\BEXE2\\JucieAndFlower.Service\\JucieAndFlower.Service.csproj", "projectName": "JucieAndFlower.Service", "projectPath": "E:\\CODE-THUE\\DATN\\exe\\exe\\BEXE2\\JucieAndFlower.Service\\JucieAndFlower.Service.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\CODE-THUE\\DATN\\exe\\exe\\BEXE2\\JucieAndFlower.Service\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Users\\<USER>\\Documents\\Infragistics\\NuGet\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"E:\\CODE-THUE\\DATN\\exe\\exe\\BEXE2\\JucieAndFlower.Data\\JucieAndFlower.Data.csproj": {"projectPath": "E:\\CODE-THUE\\DATN\\exe\\exe\\BEXE2\\JucieAndFlower.Data\\JucieAndFlower.Data.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.15, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.15, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.15, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.0.15, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.15, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.2, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[8.9.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.100/PortableRuntimeIdentifierGraph.json"}}}, "E:\\CODE-THUE\\DATN\\exe\\exe\\BEXE2\\JucieAndFlower\\JucieAndFlower.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\CODE-THUE\\DATN\\exe\\exe\\BEXE2\\JucieAndFlower\\JucieAndFlower.csproj", "projectName": "JucieAnd<PERSON>lower", "projectPath": "E:\\CODE-THUE\\DATN\\exe\\exe\\BEXE2\\JucieAndFlower\\JucieAndFlower.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\CODE-THUE\\DATN\\exe\\exe\\BEXE2\\JucieAndFlower\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Users\\<USER>\\Documents\\Infragistics\\NuGet\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"E:\\CODE-THUE\\DATN\\exe\\exe\\BEXE2\\JucieAndFlower.Service\\JucieAndFlower.Service.csproj": {"projectPath": "E:\\CODE-THUE\\DATN\\exe\\exe\\BEXE2\\JucieAndFlower.Service\\JucieAndFlower.Service.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.15, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.15, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.15, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.0.15, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.15, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[8.0.1, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.6.2, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[8.9.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.100/PortableRuntimeIdentifierGraph.json"}}}}}