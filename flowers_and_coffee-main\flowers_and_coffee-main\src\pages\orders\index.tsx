import React, { useState, useEffect } from "react";
import {
  Container,
  Paper,
  Typography,
  Box,
  Grid,
  Card,
  CardContent,
  Chip,
  Button,
  CircularProgress,
  <PERSON><PERSON>,
  Divider,
} from "@mui/material";
import { Receipt, LocalShipping, Payment, Visibility } from "@mui/icons-material";
import { useAppSelector } from "../../stores/hooks";
import { ordersAPI } from "../../services/api";
import { toast } from "react-toastify";
import { useNavigate } from "react-router-dom";

interface Order {
  orderId: number;
  orderDate: string;
  totalAmount: number;
  discountAmount: number;
  finalAmount: number;
  status: string;
  note: string;
  deliveryAddress: string;
  promotionCode?: string;
}

const OrdersPage = () => {
  const { isAuthenticated } = useAppSelector((state) => state.Authentication);
  const navigate = useNavigate();

  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/auth/login');
      return;
    }
    loadOrders();
  }, [isAuthenticated, navigate]);

  const loadOrders = async () => {
    try {
      setLoading(true);
      setError("");
      const ordersData = await ordersAPI.getOrders();
      setOrders(ordersData);
    } catch (error: any) {
      console.error('Error loading orders:', error);
      const errorMessage = error.message || "Không thể tải danh sách đơn hàng";
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'pending':
        return 'warning';
      case 'completed':
      case 'paid':
        return 'success';
      case 'cancelled':
      case 'cancel':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'pending':
        return 'Đang xử lý';
      case 'completed':
        return 'Hoàn thành';
      case 'paid':
        return 'Đã thanh toán';
      case 'cancelled':
      case 'cancel':
        return 'Đã hủy';
      default:
        return status;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (!isAuthenticated) {
    return (
      <Container maxWidth="md" sx={{ mt: 4 }}>
        <Alert severity="warning">
          Vui lòng đăng nhập để xem đơn hàng
        </Alert>
      </Container>
    );
  }

  if (loading) {
    return (
      <Container maxWidth="md" sx={{ mt: 4, textAlign: "center" }}>
        <CircularProgress />
        <Typography variant="h6" sx={{ mt: 2 }}>
          Đang tải đơn hàng...
        </Typography>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {/* Header */}
      <Paper
        elevation={0}
        sx={{
          mb: 4,
          p: 4,
          background: "linear-gradient(135deg, #9e655c 0%, #b8756b 100%)",
          color: "white",
          borderRadius: 4,
          position: "relative",
          overflow: "hidden",
          "&::before": {
            content: '""',
            position: "absolute",
            top: 0,
            right: 0,
            width: "200px",
            height: "200px",
            background: "rgba(255,255,255,0.1)",
            borderRadius: "50%",
            transform: "translate(50%, -50%)",
          },
        }}
      >
        <Box display="flex" alignItems="center" sx={{ position: "relative", zIndex: 1 }}>
          <Box
            sx={{
              bgcolor: "rgba(255,255,255,0.2)",
              borderRadius: "50%",
              p: 1.5,
              mr: 3,
            }}
          >
            <Receipt sx={{ fontSize: 32, color: "white" }} />
          </Box>
          <Box>
            <Typography variant="h4" fontWeight="bold" sx={{ mb: 0.5 }}>
              Đơn hàng của tôi
            </Typography>
            <Typography variant="body1" sx={{ opacity: 0.9, fontSize: "1.1rem" }}>
              Theo dõi trạng thái đơn hàng của bạn
            </Typography>
          </Box>
        </Box>
      </Paper>

      {error && (
        <Alert
          severity="error"
          sx={{
            mb: 3,
            borderRadius: 3,
          }}
        >
          {error}
        </Alert>
      )}

      {orders.length === 0 ? (
        <Paper
          elevation={2}
          sx={{
            p: 6,
            textAlign: "center",
            borderRadius: 4,
          }}
        >
          <Receipt sx={{ fontSize: 80, color: "text.secondary", mb: 2 }} />
          <Typography variant="h5" gutterBottom>
            Chưa có đơn hàng nào
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
            Bạn chưa có đơn hàng nào. Hãy bắt đầu mua sắm ngay!
          </Typography>
          <Button
            variant="contained"
            onClick={() => navigate("/flower")}
            sx={{
              bgcolor: "#9e655c",
              "&:hover": { bgcolor: "#8a5a52" },
              borderRadius: 3,
              px: 4,
              py: 1.5,
            }}
          >
            Bắt đầu mua sắm
          </Button>
        </Paper>
      ) : (
        <Grid container spacing={3}>
          {orders.map((order) => (
            <Grid item xs={12} key={order.orderId}>
              <Card
                elevation={2}
                sx={{
                  borderRadius: 4,
                  transition: "all 0.3s ease",
                  "&:hover": {
                    transform: "translateY(-2px)",
                    boxShadow: 6,
                  },
                }}
              >
                <CardContent sx={{ p: 3 }}>
                  <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                    <Box>
                      <Typography variant="h6" fontWeight="bold" color="#9e655c">
                        Đơn hàng #{order.orderId}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {formatDate(order.orderDate)}
                      </Typography>
                    </Box>
                    <Chip
                      label={getStatusText(order.status)}
                      color={getStatusColor(order.status) as any}
                      sx={{ fontWeight: 'bold' }}
                    />
                  </Box>

                  <Divider sx={{ my: 2 }} />

                  <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                      <Box display="flex" alignItems="center" mb={1}>
                        <LocalShipping sx={{ mr: 1, color: "#9e655c" }} />
                        <Typography variant="body2" fontWeight="bold">
                          Địa chỉ giao hàng:
                        </Typography>
                      </Box>
                      <Typography variant="body2" color="text.secondary">
                        {order.deliveryAddress}
                      </Typography>
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <Box display="flex" alignItems="center" mb={1}>
                        <Payment sx={{ mr: 1, color: "#9e655c" }} />
                        <Typography variant="body2" fontWeight="bold">
                          Tổng tiền:
                        </Typography>
                      </Box>
                      <Typography variant="h6" color="#9e655c" fontWeight="bold">
                        {formatCurrency(order.finalAmount)}
                      </Typography>
                      {order.discountAmount > 0 && (
                        <Typography variant="body2" color="text.secondary">
                          Giảm giá: {formatCurrency(order.discountAmount)}
                        </Typography>
                      )}
                    </Grid>
                  </Grid>

                  {order.note && (
                    <Box mt={2}>
                      <Typography variant="body2" fontWeight="bold" mb={0.5}>
                        Ghi chú:
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {order.note}
                      </Typography>
                    </Box>
                  )}

                  <Box display="flex" justifyContent="flex-end" mt={3}>
                    <Button
                      variant="outlined"
                      startIcon={<Visibility />}
                      onClick={() => navigate(`/orders/${order.orderId}`)}
                      sx={{
                        borderColor: "#9e655c",
                        color: "#9e655c",
                        "&:hover": {
                          borderColor: "#8a5a52",
                          bgcolor: "rgba(158, 101, 92, 0.04)",
                        },
                      }}
                    >
                      Xem chi tiết
                    </Button>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}
    </Container>
  );
};

export default OrdersPage;
