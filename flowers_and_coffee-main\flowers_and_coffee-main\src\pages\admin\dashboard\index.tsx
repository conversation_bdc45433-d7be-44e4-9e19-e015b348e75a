import React, { useState } from "react";
import {
  Grid,
  Paper,
  Typography,
  Box,
  Card,
  CardContent,
  Button,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Chip,
  LinearProgress,
  TextField,
  MenuItem,
  Divider,
  Avatar,
  IconButton,
} from "@mui/material";
import {
  Dashboard as DashboardIcon,
  TrendingUp,
  ShoppingCart,
  People,
  AttachMoney,
  Assessment,
  LocalShipping,
  Refresh,
  ArrowUpward,
} from "@mui/icons-material";
import { useAppSelector } from "../../../stores/hooks";
import { useNavigate } from "react-router-dom";



const AdminDashboard = () => {
  const { user } = useAppSelector((state) => state.Authentication);
  const navigate = useNavigate();
  const [dateFilter, setDateFilter] = useState("7days");

  // Fixed revenue data for demonstration
  const revenueData = [
    { date: "2024-01-15", revenue: 2500000, orders: 15 },
    { date: "2024-01-16", revenue: 3200000, orders: 22 },
    { date: "2024-01-17", revenue: 1800000, orders: 12 },
    { date: "2024-01-18", revenue: 4100000, orders: 28 },
    { date: "2024-01-19", revenue: 3600000, orders: 25 },
    { date: "2024-01-20", revenue: 2900000, orders: 19 },
    { date: "2024-01-21", revenue: 3800000, orders: 26 },
  ];

  // Top selling products (fixed data)
  const topProducts = [
    { name: "Hoa hồng đỏ", sold: 45, revenue: 4500000, growth: 12 },
    { name: "Cà phê Americano", sold: 38, revenue: 3800000, growth: 8 },
    { name: "Hoa ly trắng", sold: 32, revenue: 3200000, growth: -3 },
    { name: "Trà sữa matcha", sold: 29, revenue: 2900000, growth: 15 },
    { name: "Hoa cúc vàng", sold: 25, revenue: 2500000, growth: 5 },
  ];

  // Recent orders (fixed data)
  const recentOrders = [
    { id: "ORD001", customer: "Nguyễn Văn A", amount: 450000, status: "completed", date: "2024-01-21" },
    { id: "ORD002", customer: "Trần Thị B", amount: 320000, status: "pending", date: "2024-01-21" },
    { id: "ORD003", customer: "Lê Văn C", amount: 680000, status: "shipping", date: "2024-01-20" },
    { id: "ORD004", customer: "Phạm Thị D", amount: 290000, status: "completed", date: "2024-01-20" },
    { id: "ORD005", customer: "Hoàng Văn E", amount: 520000, status: "pending", date: "2024-01-19" },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed": return "success";
      case "pending": return "warning";
      case "shipping": return "info";
      case "cancelled": return "error";
      default: return "default";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "completed": return "Hoàn thành";
      case "pending": return "Chờ xử lý";
      case "shipping": return "Đang giao";
      case "cancelled": return "Đã hủy";
      default: return status;
    }
  };



  return (
    <Box>
      {/* Header */}
      <Box display="flex" alignItems="center" justifyContent="space-between" mb={4}>
        <Box display="flex" alignItems="center">
          <Avatar sx={{ width: 60, height: 60, mr: 3, bgcolor: "#9e655c" }}>
            <DashboardIcon sx={{ fontSize: 30 }} />
          </Avatar>
          <Box>
            <Typography variant="h4" component="h1" fontWeight="600" color="#333">
              Bảng điều khiển Admin
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Chào mừng trở lại, {user?.fullName || "Admin"}
            </Typography>
          </Box>
        </Box>
        <IconButton sx={{ color: "#9e655c" }}>
          <Refresh />
        </IconButton>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={3}>
          <Card elevation={1} sx={{ border: '1px solid #e0e0e0' }}>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Tổng đơn hàng
                  </Typography>
                  <Typography variant="h4" fontWeight="600" color="#333">
                    150
                  </Typography>
                  <Box display="flex" alignItems="center" mt={1}>
                    <ArrowUpward sx={{ fontSize: 16, mr: 0.5, color: "#4caf50" }} />
                    <Typography variant="body2" color="#4caf50">+12% so với tháng trước</Typography>
                  </Box>
                </Box>
                <Avatar sx={{ bgcolor: "#9e655c", width: 56, height: 56 }}>
                  <ShoppingCart sx={{ fontSize: 28 }} />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card elevation={1} sx={{ border: '1px solid #e0e0e0' }}>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Doanh thu (7 ngày)
                  </Typography>
                  <Typography variant="h4" fontWeight="600" color="#333">
                    22M
                  </Typography>
                  <Box display="flex" alignItems="center" mt={1}>
                    <ArrowUpward sx={{ fontSize: 16, mr: 0.5, color: "#4caf50" }} />
                    <Typography variant="body2" color="#4caf50">+12% so với tuần trước</Typography>
                  </Box>
                </Box>
                <Avatar sx={{ bgcolor: "#4caf50", width: 56, height: 56 }}>
                  <AttachMoney sx={{ fontSize: 28 }} />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card elevation={1} sx={{ border: '1px solid #e0e0e0' }}>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Sản phẩm
                  </Typography>
                  <Typography variant="h4" fontWeight="600" color="#333">
                    45
                  </Typography>
                  <Box display="flex" alignItems="center" mt={1}>
                    <ArrowUpward sx={{ fontSize: 16, mr: 0.5, color: "#ff9800" }} />
                    <Typography variant="body2" color="#ff9800">+5 sản phẩm mới</Typography>
                  </Box>
                </Box>
                <Avatar sx={{ bgcolor: "#ff9800", width: 56, height: 56 }}>
                  <TrendingUp sx={{ fontSize: 28 }} />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card elevation={1} sx={{ border: '1px solid #e0e0e0' }}>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Người dùng
                  </Typography>
                  <Typography variant="h4" fontWeight="600" color="#333">
                    320
                  </Typography>
                  <Box display="flex" alignItems="center" mt={1}>
                    <ArrowUpward sx={{ fontSize: 16, mr: 0.5, color: "#2196f3" }} />
                    <Typography variant="body2" color="#2196f3">+25 người dùng mới</Typography>
                  </Box>
                </Box>
                <Avatar sx={{ bgcolor: "#2196f3", width: 56, height: 56 }}>
                  <People sx={{ fontSize: 28 }} />
                </Avatar>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Revenue Analytics */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} lg={8}>
          <Paper elevation={1} sx={{ p: 3, border: '1px solid #e0e0e0' }}>
            <Box
              display="flex"
              justifyContent="space-between"
              alignItems={{ xs: "flex-start", sm: "center" }}
              flexDirection={{ xs: "column", sm: "row" }}
              gap={2}
              mb={3}
            >
              <Typography variant="h6" fontWeight="600" color="#333">
                Doanh thu theo ngày
              </Typography>
              <TextField
                select
                size="small"
                value={dateFilter}
                onChange={(e) => setDateFilter(e.target.value)}
                sx={{ minWidth: 120 }}
              >
                <MenuItem value="7days">7 ngày</MenuItem>
                <MenuItem value="30days">30 ngày</MenuItem>
                <MenuItem value="90days">90 ngày</MenuItem>
              </TextField>
            </Box>
            <Box sx={{ overflowX: "auto" }}>
              <Table size="small" sx={{ minWidth: 500 }}>
                <TableHead>
                  <TableRow>
                    <TableCell sx={{ fontWeight: 600 }}>Ngày</TableCell>
                    <TableCell align="right" sx={{ fontWeight: 600 }}>Doanh thu</TableCell>
                    <TableCell align="right" sx={{ fontWeight: 600 }}>Đơn hàng</TableCell>
                    <TableCell align="right" sx={{ fontWeight: 600 }}>Trung bình/đơn</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {revenueData.map((row) => (
                    <TableRow key={row.date} hover>
                      <TableCell>
                        {new Date(row.date).toLocaleDateString("vi-VN")}
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="body2" color="#4caf50" fontWeight="600">
                          {row.revenue.toLocaleString("vi-VN")}đ
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="body2" fontWeight="500">
                          {row.orders}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="body2" color="text.secondary">
                          {Math.round(row.revenue / row.orders).toLocaleString("vi-VN")}đ
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </Box>
          </Paper>
        </Grid>

        <Grid item xs={12} lg={4}>
          <Paper elevation={1} sx={{ p: 3, border: '1px solid #e0e0e0' }}>
            <Typography variant="h6" gutterBottom fontWeight="600" color="#333">
              Sản phẩm bán chạy
            </Typography>
            {topProducts.map((product, index) => (
              <Box key={index} mb={2}>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                  <Typography variant="body2" fontWeight="600" color="#333">
                    {product.name}
                  </Typography>
                  <Chip
                    label={`${product.growth > 0 ? '+' : ''}${product.growth}%`}
                    color={product.growth > 0 ? "success" : product.growth < 0 ? "error" : "default"}
                    size="small"
                    sx={{ fontWeight: 500 }}
                  />
                </Box>
                <Box display="flex" justifyContent="space-between" mb={1}>
                  <Typography variant="body2" color="text.secondary">
                    Đã bán: {product.sold}
                  </Typography>
                  <Typography variant="body2" color="#4caf50" fontWeight="600">
                    {product.revenue.toLocaleString("vi-VN")}đ
                  </Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={(product.sold / 50) * 100}
                  sx={{
                    height: 8,
                    borderRadius: 4,
                    bgcolor: "#f5f5f5",
                    "& .MuiLinearProgress-bar": {
                      bgcolor: "#4caf50"
                    }
                  }}
                />
                {index < topProducts.length - 1 && <Divider sx={{ mt: 2 }} />}
              </Box>
            ))}
          </Paper>
        </Grid>
      </Grid>

      {/* Recent Orders */}
      <Paper elevation={1} sx={{ p: 3, mb: 3, border: '1px solid #e0e0e0' }}>
        <Typography variant="h6" gutterBottom fontWeight="600" color="#333">
          Đơn hàng gần đây
        </Typography>
        <Box sx={{ overflowX: "auto" }}>
          <Table sx={{ minWidth: 650 }}>
            <TableHead>
              <TableRow>
                <TableCell sx={{ fontWeight: 600 }}>Mã đơn</TableCell>
                <TableCell sx={{ fontWeight: 600 }}>Khách hàng</TableCell>
                <TableCell align="right" sx={{ fontWeight: 600 }}>Số tiền</TableCell>
                <TableCell sx={{ fontWeight: 600 }}>Trạng thái</TableCell>
                <TableCell sx={{ fontWeight: 600 }}>Ngày</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {recentOrders.map((order) => (
                <TableRow key={order.id} hover>
                  <TableCell>
                    <Typography variant="body2" fontWeight="600" color="#333">
                      {order.id}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" color="#333">
                      {order.customer}
                    </Typography>
                  </TableCell>
                  <TableCell align="right">
                    <Typography variant="body2" fontWeight="600" color="#4caf50">
                      {order.amount.toLocaleString("vi-VN")}đ
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={getStatusText(order.status)}
                      color={getStatusColor(order.status) as any}
                      size="small"
                      sx={{ fontWeight: 500 }}
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" color="text.secondary">
                      {new Date(order.date).toLocaleDateString("vi-VN")}
                    </Typography>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </Box>
      </Paper>

      {/* Quick Actions */}
      <Paper elevation={1} sx={{ p: 3, border: '1px solid #e0e0e0' }}>
        <Typography variant="h6" gutterBottom fontWeight="600" color="#333">
          Thao tác nhanh
        </Typography>
        <Box
          display="flex"
          gap={2}
          flexWrap="wrap"
          sx={{
            "& .MuiButton-root": {
              borderRadius: 1,
              textTransform: "none",
              fontWeight: 500,
            }
          }}
        >
          <Button
            variant="contained"
            onClick={() => navigate("/admin/products")}
            startIcon={<Assessment />}
            sx={{
              bgcolor: "#9e655c",
              "&:hover": { bgcolor: "#8a5a52" },
            }}
          >
            Quản lý sản phẩm
          </Button>
          <Button
            variant="outlined"
            onClick={() => navigate("/admin/orders")}
            startIcon={<LocalShipping />}
            sx={{
              borderColor: "#9e655c",
              color: "#9e655c",
              "&:hover": {
                borderColor: "#8a5a52",
                bgcolor: "rgba(158, 101, 92, 0.04)",
              },
            }}
          >
            Quản lý đơn hàng
          </Button>
          <Button
            variant="outlined"
            onClick={() => navigate("/admin/users")}
            startIcon={<People />}
            sx={{
              borderColor: "#9e655c",
              color: "#9e655c",
              "&:hover": {
                borderColor: "#8a5a52",
                bgcolor: "rgba(158, 101, 92, 0.04)",
              },
            }}
          >
            Quản lý người dùng
          </Button>
        </Box>
      </Paper>
    </Box>
  );
};

export default AdminDashboard;
