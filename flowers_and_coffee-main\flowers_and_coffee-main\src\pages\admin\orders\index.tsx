import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Box,
  Chip,
  Alert,
  CircularProgress,
  TextField,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  Card,
  CardContent,
} from "@mui/material";
import {
  Visibility,
  Edit,
  LocalShipping,
  CheckCircle,
  Cancel,
  Refresh,
} from "@mui/icons-material";
import { toast } from "react-toastify";

interface Order {
  id: string;
  customerName: string;
  customerEmail: string;
  totalAmount: number;
  status: string;
  createdAt: string;
  items: Array<{
    productName: string;
    quantity: number;
    price: number;
  }>;
}

const AdminOrders: React.FC = () => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [detailDialogO<PERSON>, setDetailDialogOpen] = useState(false);
  const [statusFilter, setStatusFilter] = useState("all");

  // Mock data for demonstration
  useEffect(() => {
    const mockOrders: Order[] = [
      {
        id: "ORD001",
        customerName: "Nguyễn Văn A",
        customerEmail: "<EMAIL>",
        totalAmount: 450000,
        status: "pending",
        createdAt: "2024-01-21T10:30:00Z",
        items: [
          { productName: "Hoa hồng đỏ", quantity: 2, price: 150000 },
          { productName: "Cà phê Americano", quantity: 3, price: 50000 },
        ],
      },
      {
        id: "ORD002",
        customerName: "Trần Thị B",
        customerEmail: "<EMAIL>",
        totalAmount: 320000,
        status: "shipping",
        createdAt: "2024-01-21T09:15:00Z",
        items: [
          { productName: "Hoa ly trắng", quantity: 1, price: 200000 },
          { productName: "Trà sữa matcha", quantity: 2, price: 60000 },
        ],
      },
      {
        id: "ORD003",
        customerName: "Lê Văn C",
        customerEmail: "<EMAIL>",
        totalAmount: 680000,
        status: "completed",
        createdAt: "2024-01-20T14:45:00Z",
        items: [
          { productName: "Hoa cúc vàng", quantity: 3, price: 120000 },
          { productName: "Cappuccino", quantity: 4, price: 80000 },
        ],
      },
    ];

    setTimeout(() => {
      setOrders(mockOrders);
      setLoading(false);
    }, 1000);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed": return "success";
      case "pending": return "warning";
      case "shipping": return "info";
      case "cancelled": return "error";
      default: return "default";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "completed": return "Hoàn thành";
      case "pending": return "Chờ xử lý";
      case "shipping": return "Đang giao";
      case "cancelled": return "Đã hủy";
      default: return status;
    }
  };

  const handleStatusChange = (orderId: string, newStatus: string) => {
    setOrders(prev => 
      prev.map(order => 
        order.id === orderId ? { ...order, status: newStatus } : order
      )
    );
    toast.success(`Đã cập nhật trạng thái đơn hàng ${orderId}`);
  };

  const handleViewDetails = (order: Order) => {
    setSelectedOrder(order);
    setDetailDialogOpen(true);
  };

  const filteredOrders = statusFilter === "all" 
    ? orders 
    : orders.filter(order => order.status === statusFilter);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={4}>
        <Typography
          variant="h4"
          sx={{
            fontWeight: 700,
            color: "#333",
          }}
        >
          📦 Quản lý đơn hàng
        </Typography>
        <Button
          variant="outlined"
          startIcon={<Refresh />}
          onClick={() => window.location.reload()}
          sx={{
            borderColor: "#9e655c",
            color: "#9e655c",
            "&:hover": {
              borderColor: "#8a5a52",
              bgcolor: "rgba(158, 101, 92, 0.04)",
            },
          }}
        >
          Làm mới
        </Button>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ borderRadius: 3, border: "1px solid #f0f0f0" }}>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                Tổng đơn hàng
              </Typography>
              <Typography variant="h4" fontWeight="600" color="#333">
                {orders.length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ borderRadius: 3, border: "1px solid #f0f0f0" }}>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                Chờ xử lý
              </Typography>
              <Typography variant="h4" fontWeight="600" color="#ff9800">
                {orders.filter(o => o.status === "pending").length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ borderRadius: 3, border: "1px solid #f0f0f0" }}>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                Đang giao
              </Typography>
              <Typography variant="h4" fontWeight="600" color="#2196f3">
                {orders.filter(o => o.status === "shipping").length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ borderRadius: 3, border: "1px solid #f0f0f0" }}>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                Hoàn thành
              </Typography>
              <Typography variant="h4" fontWeight="600" color="#4caf50">
                {orders.filter(o => o.status === "completed").length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filter */}
      <Box mb={3}>
        <TextField
          select
          label="Lọc theo trạng thái"
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          size="small"
          sx={{ minWidth: 200 }}
        >
          <MenuItem value="all">Tất cả</MenuItem>
          <MenuItem value="pending">Chờ xử lý</MenuItem>
          <MenuItem value="shipping">Đang giao</MenuItem>
          <MenuItem value="completed">Hoàn thành</MenuItem>
          <MenuItem value="cancelled">Đã hủy</MenuItem>
        </TextField>
      </Box>

      {/* Orders Table */}
      <Paper sx={{ borderRadius: 3, border: "1px solid #f0f0f0" }}>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell sx={{ fontWeight: 600 }}>Mã đơn</TableCell>
                <TableCell sx={{ fontWeight: 600 }}>Khách hàng</TableCell>
                <TableCell sx={{ fontWeight: 600 }}>Email</TableCell>
                <TableCell align="right" sx={{ fontWeight: 600 }}>Tổng tiền</TableCell>
                <TableCell sx={{ fontWeight: 600 }}>Trạng thái</TableCell>
                <TableCell sx={{ fontWeight: 600 }}>Ngày tạo</TableCell>
                <TableCell align="center" sx={{ fontWeight: 600 }}>Thao tác</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredOrders.map((order) => (
                <TableRow key={order.id} hover>
                  <TableCell>
                    <Typography variant="body2" fontWeight="600">
                      {order.id}
                    </Typography>
                  </TableCell>
                  <TableCell>{order.customerName}</TableCell>
                  <TableCell>{order.customerEmail}</TableCell>
                  <TableCell align="right">
                    <Typography variant="body2" fontWeight="600" color="#4caf50">
                      {order.totalAmount.toLocaleString("vi-VN")}đ
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={getStatusText(order.status)}
                      color={getStatusColor(order.status) as any}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    {new Date(order.createdAt).toLocaleDateString("vi-VN")}
                  </TableCell>
                  <TableCell align="center">
                    <IconButton
                      size="small"
                      onClick={() => handleViewDetails(order)}
                      sx={{ mr: 1 }}
                    >
                      <Visibility />
                    </IconButton>
                    {order.status === "pending" && (
                      <IconButton
                        size="small"
                        onClick={() => handleStatusChange(order.id, "shipping")}
                        sx={{ color: "#2196f3" }}
                      >
                        <LocalShipping />
                      </IconButton>
                    )}
                    {order.status === "shipping" && (
                      <IconButton
                        size="small"
                        onClick={() => handleStatusChange(order.id, "completed")}
                        sx={{ color: "#4caf50" }}
                      >
                        <CheckCircle />
                      </IconButton>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      {/* Order Detail Dialog */}
      <Dialog
        open={detailDialogOpen}
        onClose={() => setDetailDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Chi tiết đơn hàng {selectedOrder?.id}</DialogTitle>
        <DialogContent>
          {selectedOrder && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Thông tin khách hàng
              </Typography>
              <Typography>Tên: {selectedOrder.customerName}</Typography>
              <Typography>Email: {selectedOrder.customerEmail}</Typography>
              <Typography mb={2}>
                Ngày đặt: {new Date(selectedOrder.createdAt).toLocaleString("vi-VN")}
              </Typography>

              <Typography variant="h6" gutterBottom>
                Sản phẩm
              </Typography>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Sản phẩm</TableCell>
                    <TableCell align="right">Số lượng</TableCell>
                    <TableCell align="right">Đơn giá</TableCell>
                    <TableCell align="right">Thành tiền</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {selectedOrder.items.map((item, index) => (
                    <TableRow key={index}>
                      <TableCell>{item.productName}</TableCell>
                      <TableCell align="right">{item.quantity}</TableCell>
                      <TableCell align="right">
                        {item.price.toLocaleString("vi-VN")}đ
                      </TableCell>
                      <TableCell align="right">
                        {(item.quantity * item.price).toLocaleString("vi-VN")}đ
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              <Box mt={2} textAlign="right">
                <Typography variant="h6">
                  Tổng cộng: {selectedOrder.totalAmount.toLocaleString("vi-VN")}đ
                </Typography>
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDetailDialogOpen(false)}>Đóng</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AdminOrders;
