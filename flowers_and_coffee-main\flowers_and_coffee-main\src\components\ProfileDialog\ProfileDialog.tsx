import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Box,
  Typography,
  IconButton,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
} from '@mui/material';
import { Close as CloseIcon } from '@mui/icons-material';
import { useAppSelector, useAppDispatch } from '../../stores/hooks';
import { updateProfile } from '../../stores/reducers/Authentication';
import { authAPI } from '../../services/api';
import { toast } from 'react-toastify';

interface ProfileDialogProps {
  open: boolean;
  onClose: () => void;
}

interface ProfileFormData {
  fullName: string;
  phone: string;
  address: string;
  gender: string;
  birthDate: string;
  oldPassword: string;
  newPassword: string;
  confirmPassword: string;
}

const ProfileDialog: React.FC<ProfileDialogProps> = ({ open, onClose }) => {
  const { user } = useAppSelector((state) => state.Authentication);
  const dispatch = useAppDispatch();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  
  const [formData, setFormData] = useState<ProfileFormData>({
    fullName: '',
    phone: '',
    address: '',
    gender: '',
    birthDate: '',
    oldPassword: '',
    newPassword: '',
    confirmPassword: '',
  });

  // Load user data when dialog opens
  useEffect(() => {
    if (open && user) {
      setFormData({
        fullName: user.fullName || '',
        phone: user.phone || '',
        address: user.address || '',
        gender: user.gender || '',
        birthDate: user.birthDate ? user.birthDate.split('T')[0] : '',
        oldPassword: '',
        newPassword: '',
        confirmPassword: '',
      });
      setError('');
      setSuccess('');
    }
  }, [open, user]);

  const handleInputChange = (field: keyof ProfileFormData) => (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | any
  ) => {
    const value = event.target.value;
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    setError('');
    setSuccess('');
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);
      setError('');
      setSuccess('');

      // Validate passwords if changing password
      if (formData.newPassword || formData.confirmPassword || formData.oldPassword) {
        if (!formData.oldPassword) {
          setError('Vui lòng nhập mật khẩu cũ');
          return;
        }
        if (!formData.newPassword) {
          setError('Vui lòng nhập mật khẩu mới');
          return;
        }
        if (formData.newPassword !== formData.confirmPassword) {
          setError('Mật khẩu xác nhận không khớp');
          return;
        }
        if (formData.newPassword.length < 6) {
          setError('Mật khẩu mới phải có ít nhất 6 ký tự');
          return;
        }
      }

      // Prepare update data
      const updateData: any = {
        fullName: formData.fullName,
        phone: formData.phone,
        address: formData.address,
        gender: formData.gender,
        birthDate: formData.birthDate,
      };

      // Add password fields if changing password
      if (formData.oldPassword && formData.newPassword) {
        updateData.oldPassword = formData.oldPassword;
        updateData.newPassword = formData.newPassword;
        updateData.confirmPassword = formData.confirmPassword;
      }

      // Call update API (returns success message)
      await authAPI.updateProfile(updateData);

      // Get fresh user data after update
      const updatedUser = await authAPI.getProfile();
      dispatch(updateProfile(updatedUser));

      setSuccess('Cập nhật thông tin thành công!');
      toast.success('Cập nhật thông tin thành công!');

      // Reset password fields
      setFormData(prev => ({
        ...prev,
        oldPassword: '',
        newPassword: '',
        confirmPassword: '',
      }));

    } catch (error: any) {
      console.error('Update profile error:', error);
      setError(error.message || 'Có lỗi xảy ra khi cập nhật thông tin');
      toast.error(error.message || 'Cập nhật thông tin thất bại');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      onClose();
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          backgroundImage: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
        }
      }}
    >
      <DialogTitle
        sx={{
          background: 'linear-gradient(135deg, #9e655c 0%, #b8766b 100%)',
          color: 'white',
          position: 'relative',
          textAlign: 'center',
          py: 2,
        }}
      >
        <Typography variant="h5" component="div" sx={{ fontWeight: 600 }}>
          Cập nhật thông tin cá nhân
        </Typography>
        <IconButton
          onClick={handleClose}
          disabled={loading}
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: 'white',
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ p: 3 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}
        
        {success && (
          <Alert severity="success" sx={{ mb: 2 }}>
            {success}
          </Alert>
        )}

        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          {/* Personal Information */}
          <Typography variant="h6" sx={{ mb: 1, color: '#9e655c', fontWeight: 600 }}>
            Thông tin cá nhân
          </Typography>

          <TextField
            fullWidth
            label="Họ và tên"
            value={formData.fullName}
            onChange={handleInputChange('fullName')}
            disabled={loading}
            sx={{
              '& .MuiOutlinedInput-root': {
                backgroundColor: 'rgba(255, 255, 255, 0.8)',
              }
            }}
          />

          <TextField
            fullWidth
            label="Email"
            value={user?.email || ''}
            disabled
            sx={{
              '& .MuiOutlinedInput-root': {
                backgroundColor: 'rgba(255, 255, 255, 0.5)',
              }
            }}
          />

          <TextField
            fullWidth
            label="Số điện thoại"
            value={formData.phone}
            onChange={handleInputChange('phone')}
            disabled={loading}
            sx={{
              '& .MuiOutlinedInput-root': {
                backgroundColor: 'rgba(255, 255, 255, 0.8)',
              }
            }}
          />

          <FormControl fullWidth disabled={loading}>
            <InputLabel>Giới tính</InputLabel>
            <Select
              value={formData.gender}
              label="Giới tính"
              onChange={handleInputChange('gender')}
              sx={{
                backgroundColor: 'rgba(255, 255, 255, 0.8)',
              }}
            >
              <MenuItem value="Nam">Nam</MenuItem>
              <MenuItem value="Nữ">Nữ</MenuItem>
              <MenuItem value="Khác">Khác</MenuItem>
            </Select>
          </FormControl>

          <TextField
            fullWidth
            label="Ngày sinh"
            type="date"
            value={formData.birthDate}
            onChange={handleInputChange('birthDate')}
            disabled={loading}
            InputLabelProps={{
              shrink: true,
            }}
            sx={{
              '& .MuiOutlinedInput-root': {
                backgroundColor: 'rgba(255, 255, 255, 0.8)',
              }
            }}
          />

          <TextField
            fullWidth
            label="Địa chỉ"
            multiline
            rows={2}
            value={formData.address}
            onChange={handleInputChange('address')}
            disabled={loading}
            sx={{
              '& .MuiOutlinedInput-root': {
                backgroundColor: 'rgba(255, 255, 255, 0.8)',
              }
            }}
          />

          {/* Password Change Section */}
          <Typography variant="h6" sx={{ mt: 2, mb: 1, color: '#9e655c', fontWeight: 600 }}>
            Đổi mật khẩu (tùy chọn)
          </Typography>

          <TextField
            fullWidth
            label="Mật khẩu cũ"
            type="password"
            value={formData.oldPassword}
            onChange={handleInputChange('oldPassword')}
            disabled={loading}
            sx={{
              '& .MuiOutlinedInput-root': {
                backgroundColor: 'rgba(255, 255, 255, 0.8)',
              }
            }}
          />

          <TextField
            fullWidth
            label="Mật khẩu mới"
            type="password"
            value={formData.newPassword}
            onChange={handleInputChange('newPassword')}
            disabled={loading}
            sx={{
              '& .MuiOutlinedInput-root': {
                backgroundColor: 'rgba(255, 255, 255, 0.8)',
              }
            }}
          />

          <TextField
            fullWidth
            label="Xác nhận mật khẩu mới"
            type="password"
            value={formData.confirmPassword}
            onChange={handleInputChange('confirmPassword')}
            disabled={loading}
            sx={{
              '& .MuiOutlinedInput-root': {
                backgroundColor: 'rgba(255, 255, 255, 0.8)',
              }
            }}
          />
        </Box>
      </DialogContent>

      <DialogActions sx={{ p: 3, justifyContent: 'center' }}>
        <Button
          onClick={handleClose}
          disabled={loading}
          sx={{
            mr: 2,
            px: 4,
            py: 1,
            borderRadius: 2,
            textTransform: 'none',
            fontWeight: 600,
          }}
        >
          Hủy
        </Button>
        <Button
          onClick={handleSubmit}
          disabled={loading}
          variant="contained"
          sx={{
            px: 4,
            py: 1,
            borderRadius: 2,
            textTransform: 'none',
            fontWeight: 600,
            background: 'linear-gradient(135deg, #9e655c 0%, #b8766b 100%)',
            '&:hover': {
              background: 'linear-gradient(135deg, #8a5650 0%, #a66b60 100%)',
            },
          }}
        >
          {loading ? <CircularProgress size={20} color="inherit" /> : 'Cập nhật'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ProfileDialog;
